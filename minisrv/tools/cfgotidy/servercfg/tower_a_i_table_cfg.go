// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type TowerAITableCfg struct {
	Id    int32         `json:"Id"` // Id
	AI    int32         `json:"AI"` // 英雄
	AIRef *HeroTableCfg `json:"-"`  // 英雄
}

func NewTowerAITableCfg() *TowerAITableCfg {
	return &TowerAITableCfg{
		Id:    0,
		AI:    0,
		AIRef: nil,
	}
}

func NewMockTowerAITableCfg() *TowerAITableCfg {
	return &TowerAITableCfg{
		Id:    0,
		AI:    0,
		AIRef: nil,
	}
}

type TowerAITable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*TowerAITableCfg
	localIds         map[int32]struct{}
}

func NewTowerAITable(configs *Configs) *TowerAITable {
	return &TowerAITable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*TowerAITableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *TowerAITable) Get(key int32) *TowerAITableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *TowerAITable) GetAll() map[int32]*TowerAITableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *TowerAITable) put(key int32, value *TowerAITableCfg, local bool) *TowerAITableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *TowerAITable) putFromInheritedTable(key int32, value *TowerAITableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[TowerAITable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *TowerAITable) Put(key int32, value *TowerAITableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[TowerAITable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *TowerAITable) PutAll(m map[int32]*TowerAITableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *TowerAITable) Range(f func(v *TowerAITableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *TowerAITable) Filter(filterFuncs ...func(v *TowerAITableCfg) bool) map[int32]*TowerAITableCfg {
	filtered := map[int32]*TowerAITableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *TowerAITable) FilterSlice(filterFuncs ...func(v *TowerAITableCfg) bool) []*TowerAITableCfg {
	filtered := []*TowerAITableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *TowerAITable) FilterKeys(filterFuncs ...func(v *TowerAITableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *TowerAITable) satisfied(v *TowerAITableCfg, filterFuncs ...func(v *TowerAITableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *TowerAITable) setupIndexes() error {
	return nil
}

func (t *TowerAITable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *TowerAITableCfg) bindRefs(c *Configs) {
	r.AIRef = c.HeroTable.Get(r.AI)
}

func (t *TowerAITable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[TowerAITable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewTowerAITableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [TowerAITable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[TowerAITable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// AI
		if record[t.getIndexInCsv("AI")] == "" {
			recordCfg.AI = 0
		} else {
			var err error
			recordCfg.AI, err = configs.HeroTable.getIdByRef(record[t.getIndexInCsv("AI")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [TowerAITable]unmarshal csv record failed, varName=AI, type=ref@HeroTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("AI")], err)
				} else {
					return fmt.Errorf("[TowerAITable]unmarshal csv record failed, varName=AI, type=ref@HeroTable, value=%s, err:[%s]", record[t.getIndexInCsv("AI")], err)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [TowerAITable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[TowerAITable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *TowerAITable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "TowerAITable.csv") && (!strings.HasPrefix(fileName, "TowerAITable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for TowerAITable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[TowerAITable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[TowerAITable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[TowerAITable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[TowerAITable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[TowerAITable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[TowerAITable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[TowerAITable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[TowerAITable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [TowerAITable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *TowerAITable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[TowerAITable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [TowerAITable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *TowerAITable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[TowerAITable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *TowerAITable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[TowerAITable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[TowerAITable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
