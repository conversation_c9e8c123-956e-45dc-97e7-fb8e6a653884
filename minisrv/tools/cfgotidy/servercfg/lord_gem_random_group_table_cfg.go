// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type LordGemRandomGroupTableCfg struct {
	Id                        int32         `json:"Id"`                        // Id
	FreeCD                    int32         `json:"FreeCD"`                    // 免费抽奖CD/秒
	DailyFreeTimesLimit       int32         `json:"DailyFreeTimesLimit"`       // 每日免费抽奖次数上限
	ImageBig                  string        `json:"ImageBig"`                  // 气氛大图
	ImageTag                  string        `json:"ImageTag"`                  // 标签图
	Bet                       []int32       `json:"Bet"`                       // 召唤倍数
	SingleDrawCostType        int32         `json:"SingleDrawCostType"`        // 单次召唤消耗道具类型
	SingleDrawCostTypeRef     *ItemTableCfg `json:"-"`                         // 单次召唤消耗道具类型
	SingleDrawCostValue       int32         `json:"SingleDrawCostValue"`       // 单次召唤消耗道具数量
	SingleDrawCostDiamdondCnt int32         `json:"SingleDrawCostDiamdondCnt"` // 召唤道具不足时单抽消耗钻石数量
}

func NewLordGemRandomGroupTableCfg() *LordGemRandomGroupTableCfg {
	return &LordGemRandomGroupTableCfg{
		Id:                        0,
		FreeCD:                    0,
		DailyFreeTimesLimit:       0,
		ImageBig:                  "",
		ImageTag:                  "",
		Bet:                       []int32{},
		SingleDrawCostType:        0,
		SingleDrawCostTypeRef:     nil,
		SingleDrawCostValue:       0,
		SingleDrawCostDiamdondCnt: 0,
	}
}

func NewMockLordGemRandomGroupTableCfg() *LordGemRandomGroupTableCfg {
	return &LordGemRandomGroupTableCfg{
		Id:                        0,
		FreeCD:                    0,
		DailyFreeTimesLimit:       0,
		ImageBig:                  "",
		ImageTag:                  "",
		Bet:                       []int32{0},
		SingleDrawCostType:        0,
		SingleDrawCostTypeRef:     nil,
		SingleDrawCostValue:       0,
		SingleDrawCostDiamdondCnt: 0,
	}
}

type LordGemRandomGroupTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*LordGemRandomGroupTableCfg
	localIds         map[int32]struct{}
}

func NewLordGemRandomGroupTable(configs *Configs) *LordGemRandomGroupTable {
	return &LordGemRandomGroupTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*LordGemRandomGroupTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *LordGemRandomGroupTable) Get(key int32) *LordGemRandomGroupTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LordGemRandomGroupTable) GetAll() map[int32]*LordGemRandomGroupTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LordGemRandomGroupTable) put(key int32, value *LordGemRandomGroupTableCfg, local bool) *LordGemRandomGroupTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *LordGemRandomGroupTable) putFromInheritedTable(key int32, value *LordGemRandomGroupTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[LordGemRandomGroupTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LordGemRandomGroupTable) Put(key int32, value *LordGemRandomGroupTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[LordGemRandomGroupTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LordGemRandomGroupTable) PutAll(m map[int32]*LordGemRandomGroupTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LordGemRandomGroupTable) Range(f func(v *LordGemRandomGroupTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LordGemRandomGroupTable) Filter(filterFuncs ...func(v *LordGemRandomGroupTableCfg) bool) map[int32]*LordGemRandomGroupTableCfg {
	filtered := map[int32]*LordGemRandomGroupTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LordGemRandomGroupTable) FilterSlice(filterFuncs ...func(v *LordGemRandomGroupTableCfg) bool) []*LordGemRandomGroupTableCfg {
	filtered := []*LordGemRandomGroupTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LordGemRandomGroupTable) FilterKeys(filterFuncs ...func(v *LordGemRandomGroupTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LordGemRandomGroupTable) satisfied(v *LordGemRandomGroupTableCfg, filterFuncs ...func(v *LordGemRandomGroupTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LordGemRandomGroupTable) setupIndexes() error {
	return nil
}

func (t *LordGemRandomGroupTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LordGemRandomGroupTableCfg) bindRefs(c *Configs) {
	r.SingleDrawCostTypeRef = c.ItemTable.Get(r.SingleDrawCostType)
}

func (t *LordGemRandomGroupTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewLordGemRandomGroupTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// FreeCD
		{
			if record[t.getIndexInCsv("FreeCD")] == "" {
				recordCfg.FreeCD = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("FreeCD")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, varName=FreeCD, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeCD")], err)
					} else {
						return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, varName=FreeCD, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("FreeCD")], err)
					}
				}
				recordCfg.FreeCD = int32(cfgoInt)
			}
		}
		// DailyFreeTimesLimit
		{
			if record[t.getIndexInCsv("DailyFreeTimesLimit")] == "" {
				recordCfg.DailyFreeTimesLimit = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("DailyFreeTimesLimit")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, varName=DailyFreeTimesLimit, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("DailyFreeTimesLimit")], err)
					} else {
						return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, varName=DailyFreeTimesLimit, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("DailyFreeTimesLimit")], err)
					}
				}
				recordCfg.DailyFreeTimesLimit = int32(cfgoInt)
			}
		}
		// ImageBig
		{
			recordCfg.ImageBig = strings.TrimSpace(record[t.getIndexInCsv("ImageBig")])
		}
		// ImageTag
		{
			recordCfg.ImageTag = strings.TrimSpace(record[t.getIndexInCsv("ImageTag")])
		}
		// Bet
		{
			if record[t.getIndexInCsv("Bet")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Bet")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBet int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfBet = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal record failed, cannot parse int in vector, varName=Bet, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[LordGemRandomGroupTable]unmarshal record failed, cannot parse int in vector, varName=Bet, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfBet = int32(cfgoInt)
					}

					recordCfg.Bet = append(recordCfg.Bet, cfgoElemOfBet)
				}
			}
		}
		// SingleDrawCostType
		if record[t.getIndexInCsv("SingleDrawCostType")] == "" {
			recordCfg.SingleDrawCostType = 0
		} else {
			var err error
			recordCfg.SingleDrawCostType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("SingleDrawCostType")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, varName=SingleDrawCostType, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("SingleDrawCostType")], err)
				} else {
					return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, varName=SingleDrawCostType, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("SingleDrawCostType")], err)
				}
			}
		}
		// SingleDrawCostValue
		{
			if record[t.getIndexInCsv("SingleDrawCostValue")] == "" {
				recordCfg.SingleDrawCostValue = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("SingleDrawCostValue")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, varName=SingleDrawCostValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("SingleDrawCostValue")], err)
					} else {
						return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, varName=SingleDrawCostValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("SingleDrawCostValue")], err)
					}
				}
				recordCfg.SingleDrawCostValue = int32(cfgoInt)
			}
		}
		// SingleDrawCostDiamdondCnt
		{
			if record[t.getIndexInCsv("SingleDrawCostDiamdondCnt")] == "" {
				recordCfg.SingleDrawCostDiamdondCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("SingleDrawCostDiamdondCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, varName=SingleDrawCostDiamdondCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("SingleDrawCostDiamdondCnt")], err)
					} else {
						return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, varName=SingleDrawCostDiamdondCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("SingleDrawCostDiamdondCnt")], err)
					}
				}
				recordCfg.SingleDrawCostDiamdondCnt = int32(cfgoInt)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [LordGemRandomGroupTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *LordGemRandomGroupTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "LordGemRandomGroupTable.csv") && (!strings.HasPrefix(fileName, "LordGemRandomGroupTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for LordGemRandomGroupTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[LordGemRandomGroupTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[LordGemRandomGroupTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[LordGemRandomGroupTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[LordGemRandomGroupTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[LordGemRandomGroupTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[LordGemRandomGroupTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[LordGemRandomGroupTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[LordGemRandomGroupTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [LordGemRandomGroupTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *LordGemRandomGroupTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[LordGemRandomGroupTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [LordGemRandomGroupTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *LordGemRandomGroupTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[LordGemRandomGroupTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *LordGemRandomGroupTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[LordGemRandomGroupTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[LordGemRandomGroupTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
