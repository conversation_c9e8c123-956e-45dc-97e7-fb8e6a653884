// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type Boss2ParamStr struct {
	Target   MissileTarget `json:"Target"`   // 目标
	DmgRatio float32       `json:"DmgRatio"` // 伤害系数
}

func NewBoss2ParamStr() *Boss2ParamStr {
	return &Boss2ParamStr{
		Target:   MissileTarget(enumDefaultValue),
		DmgRatio: 0.0,
	}
}

func NewMockBoss2ParamStr() *Boss2ParamStr {
	return &Boss2ParamStr{
		Target:   MissileTarget(enumDefaultValue),
		DmgRatio: 0.0,
	}
}

func (s *Boss2ParamStr) bindRefs(c *Configs) {
}
