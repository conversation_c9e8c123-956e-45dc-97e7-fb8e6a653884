// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type TriggerParamStr struct {
	IsAtkedTrigger bool                   `json:"IsAtkedTrigger"` // 被攻击触发
	Cd             float32                `json:"Cd"`             // 冷却时间，秒
	Limit          int32                  `json:"Limit"`          // 次数限制
	Buff           int32                  `json:"Buff"`           // 触发buff
	BuffRef        *HeroSkillBuffTableCfg `json:"-"`              // 触发buff
	Time           float32                `json:"Time"`           // 持续时间，秒
}

func NewTriggerParamStr() *TriggerParamStr {
	return &TriggerParamStr{
		IsAtkedTrigger: false,
		Cd:             0.0,
		Limit:          0,
		Buff:           0,
		BuffRef:        nil,
		Time:           0.0,
	}
}

func NewMockTriggerParamStr() *TriggerParamStr {
	return &TriggerParamStr{
		IsAtkedTrigger: false,
		Cd:             0.0,
		Limit:          0,
		Buff:           0,
		BuffRef:        nil,
		Time:           0.0,
	}
}

func (s *TriggerParamStr) bindRefs(c *Configs) {
	s.BuffRef = c.HeroSkillBuffTable.Get(s.Buff)
}
