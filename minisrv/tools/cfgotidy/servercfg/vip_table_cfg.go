// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type VipTableCfg struct {
	Id              int32               `json:"Id"`            // Id
	StringId        string              `json:"StringId"`      // StringId
	Level           int32               `json:"Level"`         // 等级
	Max             bool                `json:"Max"`           // 最大
	Exp             int32               `json:"Exp"`           // 升级所需累计经验
	Benefits        []int32             `json:"Benefits"`      // 特权
	BenefitsRef     []*BenefitsTableCfg `json:"-"`             // 特权
	BenefitsValue   []float32           `json:"BenefitsValue"` // 特权值
	FreeGift        []*RewardKVS        `json:"FreeGift"`      // 每日免费礼包
	IapPackageId    int32               `json:"IapPackageId"`  // VIP专属礼包
	IapPackageIdRef *IapPackageTableCfg `json:"-"`             // VIP专属礼包
	Limit           PurchaseLimitType   `json:"Limit"`         // 限购类型
	Times           int32               `json:"Times"`         // 限购次数
}

func NewVipTableCfg() *VipTableCfg {
	return &VipTableCfg{
		Id:              0,
		StringId:        "",
		Level:           0,
		Max:             false,
		Exp:             0,
		Benefits:        []int32{},
		BenefitsRef:     []*BenefitsTableCfg{},
		BenefitsValue:   []float32{},
		FreeGift:        []*RewardKVS{},
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

func NewMockVipTableCfg() *VipTableCfg {
	return &VipTableCfg{
		Id:              0,
		StringId:        "",
		Level:           0,
		Max:             false,
		Exp:             0,
		Benefits:        []int32{0},
		BenefitsRef:     []*BenefitsTableCfg{},
		BenefitsValue:   []float32{0.0},
		FreeGift:        []*RewardKVS{NewMockRewardKVS()},
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

type VipTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*VipTableCfg
	localIds         map[int32]struct{}
}

func NewVipTable(configs *Configs) *VipTable {
	return &VipTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*VipTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *VipTable) Get(key int32) *VipTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *VipTable) GetAll() map[int32]*VipTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *VipTable) put(key int32, value *VipTableCfg, local bool) *VipTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *VipTable) putFromInheritedTable(key int32, value *VipTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[VipTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *VipTable) Put(key int32, value *VipTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[VipTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *VipTable) PutAll(m map[int32]*VipTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *VipTable) Range(f func(v *VipTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *VipTable) Filter(filterFuncs ...func(v *VipTableCfg) bool) map[int32]*VipTableCfg {
	filtered := map[int32]*VipTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *VipTable) FilterSlice(filterFuncs ...func(v *VipTableCfg) bool) []*VipTableCfg {
	filtered := []*VipTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *VipTable) FilterKeys(filterFuncs ...func(v *VipTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *VipTable) satisfied(v *VipTableCfg, filterFuncs ...func(v *VipTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *VipTable) setupIndexes() error {
	return nil
}

func (t *VipTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *VipTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Benefits {
		cfgoRefRecord := c.BenefitsTable.Get(e)
		r.BenefitsRef = append(r.BenefitsRef, cfgoRefRecord)
	}
	for _, e := range r.FreeGift {
		e.bindRefs(c)
	}
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}

func (t *VipTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[VipTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewVipTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Level
		{
			if record[t.getIndexInCsv("Level")] == "" {
				recordCfg.Level = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Level")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=Level, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Level")], err)
					} else {
						return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=Level, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Level")], err)
					}
				}
				recordCfg.Level = int32(cfgoInt)
			}
		}
		// Max
		{
			if record[t.getIndexInCsv("Max")] == "" {
				recordCfg.Max = false
			} else {
				var err error
				recordCfg.Max, err = strconv.ParseBool(record[t.getIndexInCsv("Max")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=Max, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("Max")], err)
					} else {
						return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=Max, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("Max")], err)
					}
				}
			}
		}
		// Exp
		{
			if record[t.getIndexInCsv("Exp")] == "" {
				recordCfg.Exp = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Exp")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=Exp, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Exp")], err)
					} else {
						return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=Exp, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Exp")], err)
					}
				}
				recordCfg.Exp = int32(cfgoInt)
			}
		}
		// Benefits
		{
			if record[t.getIndexInCsv("Benefits")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Benefits")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBenefits int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfBenefits = 0
					} else {
						var err error
						cfgoElemOfBenefits, err = configs.BenefitsTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse ref@BenefitsTable in vector, varName=Benefits, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse ref@BenefitsTable in vector, varName=Benefits, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Benefits = append(recordCfg.Benefits, cfgoElemOfBenefits)
				}
			}
		}
		// BenefitsValue
		{
			if record[t.getIndexInCsv("BenefitsValue")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("BenefitsValue")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBenefitsValue float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfBenefitsValue = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse float in vector, varName=BenefitsValue, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse float in vector, varName=BenefitsValue, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfBenefitsValue = float32(cfgoFloat)
					}

					recordCfg.BenefitsValue = append(recordCfg.BenefitsValue, cfgoElemOfBenefitsValue)
				}
			}
		}
		// FreeGift
		{
			cfgoMeetNilForFreeGiftOfRecordCfg := false
			// element 0 of FreeGift
			if !cfgoMeetNilForFreeGiftOfRecordCfg {
				cfgoMeetNilForFreeGiftOfRecordCfg = true
				var cfgoElemOfFreeGiftOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("FreeGift1RewardType")] != "" {
						cfgoMeetNilForFreeGiftOfRecordCfg = false
						var err error
						cfgoElemOfFreeGiftOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("FreeGift1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeGift1RewardType")], err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("FreeGift1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("FreeGift1RewardValue")] != "" {
						cfgoMeetNilForFreeGiftOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("FreeGift1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeGift1RewardValue")], err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("FreeGift1RewardValue")], err)
							}
						}
						cfgoElemOfFreeGiftOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForFreeGiftOfRecordCfg {
					recordCfg.FreeGift = append(recordCfg.FreeGift, cfgoElemOfFreeGiftOfRecordCfg)
				}
			}
			// element 1 of FreeGift
			if !cfgoMeetNilForFreeGiftOfRecordCfg {
				cfgoMeetNilForFreeGiftOfRecordCfg = true
				var cfgoElemOfFreeGiftOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("FreeGift2RewardType")] != "" {
						cfgoMeetNilForFreeGiftOfRecordCfg = false
						var err error
						cfgoElemOfFreeGiftOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("FreeGift2RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeGift2RewardType")], err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("FreeGift2RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("FreeGift2RewardValue")] != "" {
						cfgoMeetNilForFreeGiftOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("FreeGift2RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeGift2RewardValue")], err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("FreeGift2RewardValue")], err)
							}
						}
						cfgoElemOfFreeGiftOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForFreeGiftOfRecordCfg {
					recordCfg.FreeGift = append(recordCfg.FreeGift, cfgoElemOfFreeGiftOfRecordCfg)
				}
			}
			// element 2 of FreeGift
			if !cfgoMeetNilForFreeGiftOfRecordCfg {
				cfgoMeetNilForFreeGiftOfRecordCfg = true
				var cfgoElemOfFreeGiftOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("FreeGift3RewardType")] != "" {
						cfgoMeetNilForFreeGiftOfRecordCfg = false
						var err error
						cfgoElemOfFreeGiftOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("FreeGift3RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeGift3RewardType")], err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("FreeGift3RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("FreeGift3RewardValue")] != "" {
						cfgoMeetNilForFreeGiftOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("FreeGift3RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [VipTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("FreeGift3RewardValue")], err)
							} else {
								return fmt.Errorf("[VipTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfFreeGiftOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("FreeGift3RewardValue")], err)
							}
						}
						cfgoElemOfFreeGiftOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForFreeGiftOfRecordCfg {
					recordCfg.FreeGift = append(recordCfg.FreeGift, cfgoElemOfFreeGiftOfRecordCfg)
				}
			}

		}
		// IapPackageId
		if record[t.getIndexInCsv("IapPackageId")] == "" {
			recordCfg.IapPackageId = 0
		} else {
			var err error
			recordCfg.IapPackageId, err = configs.IapPackageTable.getIdByRef(record[t.getIndexInCsv("IapPackageId")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("IapPackageId")], err)
				} else {
					return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]", record[t.getIndexInCsv("IapPackageId")], err)
				}
			}
		}
		// Limit
		{
			if record[t.getIndexInCsv("Limit")] == "" {
				recordCfg.Limit = PurchaseLimitType(enumDefaultValue)
			} else {
				cfgoEnum, err := parsePurchaseLimitType(record[t.getIndexInCsv("Limit")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Limit")], err)
					} else {
						return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]", record[t.getIndexInCsv("Limit")], err)
					}
				}
				recordCfg.Limit = cfgoEnum
			}
		}
		// Times
		{
			if record[t.getIndexInCsv("Times")] == "" {
				recordCfg.Times = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Times")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [VipTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Times")], err)
					} else {
						return fmt.Errorf("[VipTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Times")], err)
					}
				}
				recordCfg.Times = int32(cfgoInt)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [VipTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[VipTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *VipTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "VipTable.csv") && (!strings.HasPrefix(fileName, "VipTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for VipTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[VipTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[VipTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[VipTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[VipTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[VipTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[VipTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[VipTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[VipTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [VipTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *VipTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[VipTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [VipTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *VipTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[VipTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *VipTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[VipTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[VipTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
