// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type IapPriceTableCfg struct {
	Id       int32  `json:"Id"`       // Id
	StringId string `json:"StringId"` // StringId
	Price    string `json:"Price"`    // 价格$
	Cny      int32  `json:"Cny"`      // 价格￥
	Krw      int32  `json:"Krw"`      // 价格₩
	Diamond  int32  `json:"Diamond"`  // 钻石
	LtcScore int32  `json:"LtcScore"` // 累充活动积分
}

func NewIapPriceTableCfg() *IapPriceTableCfg {
	return &IapPriceTableCfg{
		Id:       0,
		StringId: "",
		Price:    "",
		Cny:      0,
		Krw:      0,
		Diamond:  0,
		LtcScore: 0,
	}
}

func NewMockIapPriceTableCfg() *IapPriceTableCfg {
	return &IapPriceTableCfg{
		Id:       0,
		StringId: "",
		Price:    "",
		Cny:      0,
		Krw:      0,
		Diamond:  0,
		LtcScore: 0,
	}
}

type IapPriceTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*IapPriceTableCfg
	localIds         map[int32]struct{}
}

func NewIapPriceTable(configs *Configs) *IapPriceTable {
	return &IapPriceTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*IapPriceTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *IapPriceTable) Get(key int32) *IapPriceTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapPriceTable) GetAll() map[int32]*IapPriceTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapPriceTable) put(key int32, value *IapPriceTableCfg, local bool) *IapPriceTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *IapPriceTable) putFromInheritedTable(key int32, value *IapPriceTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[IapPriceTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapPriceTable) Put(key int32, value *IapPriceTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[IapPriceTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapPriceTable) PutAll(m map[int32]*IapPriceTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapPriceTable) Range(f func(v *IapPriceTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapPriceTable) Filter(filterFuncs ...func(v *IapPriceTableCfg) bool) map[int32]*IapPriceTableCfg {
	filtered := map[int32]*IapPriceTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapPriceTable) FilterSlice(filterFuncs ...func(v *IapPriceTableCfg) bool) []*IapPriceTableCfg {
	filtered := []*IapPriceTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapPriceTable) FilterKeys(filterFuncs ...func(v *IapPriceTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapPriceTable) satisfied(v *IapPriceTableCfg, filterFuncs ...func(v *IapPriceTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapPriceTable) setupIndexes() error {
	return nil
}

func (t *IapPriceTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapPriceTableCfg) bindRefs(c *Configs) {
}

func (t *IapPriceTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[IapPriceTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewIapPriceTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapPriceTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[IapPriceTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Price
		{
			recordCfg.Price = strings.TrimSpace(record[t.getIndexInCsv("Price")])
		}
		// Cny
		{
			if record[t.getIndexInCsv("Cny")] == "" {
				recordCfg.Cny = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Cny")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapPriceTable]unmarshal csv record failed, varName=Cny, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Cny")], err)
					} else {
						return fmt.Errorf("[IapPriceTable]unmarshal csv record failed, varName=Cny, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Cny")], err)
					}
				}
				recordCfg.Cny = int32(cfgoInt)
			}
		}
		// Krw
		{
			if record[t.getIndexInCsv("Krw")] == "" {
				recordCfg.Krw = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Krw")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapPriceTable]unmarshal csv record failed, varName=Krw, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Krw")], err)
					} else {
						return fmt.Errorf("[IapPriceTable]unmarshal csv record failed, varName=Krw, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Krw")], err)
					}
				}
				recordCfg.Krw = int32(cfgoInt)
			}
		}
		// Diamond
		{
			if record[t.getIndexInCsv("Diamond")] == "" {
				recordCfg.Diamond = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Diamond")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapPriceTable]unmarshal csv record failed, varName=Diamond, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Diamond")], err)
					} else {
						return fmt.Errorf("[IapPriceTable]unmarshal csv record failed, varName=Diamond, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Diamond")], err)
					}
				}
				recordCfg.Diamond = int32(cfgoInt)
			}
		}
		// LtcScore
		{
			if record[t.getIndexInCsv("LtcScore")] == "" {
				recordCfg.LtcScore = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("LtcScore")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapPriceTable]unmarshal csv record failed, varName=LtcScore, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("LtcScore")], err)
					} else {
						return fmt.Errorf("[IapPriceTable]unmarshal csv record failed, varName=LtcScore, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("LtcScore")], err)
					}
				}
				recordCfg.LtcScore = int32(cfgoInt)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [IapPriceTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[IapPriceTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *IapPriceTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "IapPriceTable.csv") && (!strings.HasPrefix(fileName, "IapPriceTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for IapPriceTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[IapPriceTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[IapPriceTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[IapPriceTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[IapPriceTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[IapPriceTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[IapPriceTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[IapPriceTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[IapPriceTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [IapPriceTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *IapPriceTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[IapPriceTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [IapPriceTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *IapPriceTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[IapPriceTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *IapPriceTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[IapPriceTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[IapPriceTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
