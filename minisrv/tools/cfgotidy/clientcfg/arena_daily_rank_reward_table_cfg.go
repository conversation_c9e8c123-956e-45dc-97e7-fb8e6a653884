// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type ArenaDailyRankRewardTableCfg struct {
	Id            int32           `json:"Id"`          // Id
	Rank          *IntervalStr    `json:"Rank"`        // 排名区间
	RewardType    []int32         `json:"RewardType"`  // 奖励类型
	RewardTypeRef []*ItemTableCfg `json:"-"`           // 奖励类型
	RewardValue   []int32         `json:"RewardValue"` // 奖励数量
}

func NewArenaDailyRankRewardTableCfg() *ArenaDailyRankRewardTableCfg {
	return &ArenaDailyRankRewardTableCfg{
		Id:            0,
		Rank:          NewIntervalStr(),
		RewardType:    []int32{},
		RewardTypeRef: []*ItemTableCfg{},
		RewardValue:   []int32{},
	}
}

func NewMockArenaDailyRankRewardTableCfg() *ArenaDailyRankRewardTableCfg {
	return &ArenaDailyRankRewardTableCfg{
		Id:            0,
		Rank:          NewMockIntervalStr(),
		RewardType:    []int32{0},
		RewardTypeRef: []*ItemTableCfg{},
		RewardValue:   []int32{0},
	}
}

type ArenaDailyRankRewardTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*ArenaDailyRankRewardTableCfg
	localIds         map[int32]struct{}
}

func NewArenaDailyRankRewardTable(configs *Configs) *ArenaDailyRankRewardTable {
	return &ArenaDailyRankRewardTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*ArenaDailyRankRewardTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *ArenaDailyRankRewardTable) Get(key int32) *ArenaDailyRankRewardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *ArenaDailyRankRewardTable) GetAll() map[int32]*ArenaDailyRankRewardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *ArenaDailyRankRewardTable) put(key int32, value *ArenaDailyRankRewardTableCfg, local bool) *ArenaDailyRankRewardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *ArenaDailyRankRewardTable) putFromInheritedTable(key int32, value *ArenaDailyRankRewardTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[ArenaDailyRankRewardTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *ArenaDailyRankRewardTable) Put(key int32, value *ArenaDailyRankRewardTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[ArenaDailyRankRewardTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *ArenaDailyRankRewardTable) PutAll(m map[int32]*ArenaDailyRankRewardTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *ArenaDailyRankRewardTable) Range(f func(v *ArenaDailyRankRewardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *ArenaDailyRankRewardTable) Filter(filterFuncs ...func(v *ArenaDailyRankRewardTableCfg) bool) map[int32]*ArenaDailyRankRewardTableCfg {
	filtered := map[int32]*ArenaDailyRankRewardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *ArenaDailyRankRewardTable) FilterSlice(filterFuncs ...func(v *ArenaDailyRankRewardTableCfg) bool) []*ArenaDailyRankRewardTableCfg {
	filtered := []*ArenaDailyRankRewardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *ArenaDailyRankRewardTable) FilterKeys(filterFuncs ...func(v *ArenaDailyRankRewardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *ArenaDailyRankRewardTable) satisfied(v *ArenaDailyRankRewardTableCfg, filterFuncs ...func(v *ArenaDailyRankRewardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *ArenaDailyRankRewardTable) setupIndexes() error {
	return nil
}

func (t *ArenaDailyRankRewardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *ArenaDailyRankRewardTableCfg) bindRefs(c *Configs) {
	r.Rank.bindRefs(c)
	for _, e := range r.RewardType {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.RewardTypeRef = append(r.RewardTypeRef, cfgoRefRecord)
	}
}

func (t *ArenaDailyRankRewardTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewArenaDailyRankRewardTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ArenaDailyRankRewardTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// Rank
		{
			// Min
			{
				if record[t.getIndexInCsv("RankMin")] == "" {
					recordCfg.Rank.Min = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RankMin")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [ArenaDailyRankRewardTable]unmarshal csv record failed, varName=Min, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("RankMin")], err)
						} else {
							return fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal csv record failed, varName=Min, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("RankMin")], err)
						}
					}
					recordCfg.Rank.Min = int32(cfgoInt)
				}
			}
			// Max
			{
				if record[t.getIndexInCsv("RankMax")] == "" {
					recordCfg.Rank.Max = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RankMax")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [ArenaDailyRankRewardTable]unmarshal csv record failed, varName=Max, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("RankMax")], err)
						} else {
							return fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal csv record failed, varName=Max, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("RankMax")], err)
						}
					}
					recordCfg.Rank.Max = int32(cfgoInt)
				}
			}
		}
		// RewardType
		{
			if record[t.getIndexInCsv("RewardType")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("RewardType")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfRewardType int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfRewardType = 0
					} else {
						var err error
						cfgoElemOfRewardType, err = configs.ItemTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ArenaDailyRankRewardTable]unmarshal record failed, cannot parse ref@ItemTable in vector, varName=RewardType, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal record failed, cannot parse ref@ItemTable in vector, varName=RewardType, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.RewardType = append(recordCfg.RewardType, cfgoElemOfRewardType)
				}
			}
		}
		// RewardValue
		{
			if record[t.getIndexInCsv("RewardValue")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("RewardValue")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfRewardValue int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfRewardValue = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ArenaDailyRankRewardTable]unmarshal record failed, cannot parse int in vector, varName=RewardValue, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal record failed, cannot parse int in vector, varName=RewardValue, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfRewardValue = int32(cfgoInt)
					}

					recordCfg.RewardValue = append(recordCfg.RewardValue, cfgoElemOfRewardValue)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [ArenaDailyRankRewardTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *ArenaDailyRankRewardTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "ArenaDailyRankRewardTable.csv") && (!strings.HasPrefix(fileName, "ArenaDailyRankRewardTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for ArenaDailyRankRewardTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[ArenaDailyRankRewardTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[ArenaDailyRankRewardTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[ArenaDailyRankRewardTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[ArenaDailyRankRewardTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[ArenaDailyRankRewardTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[ArenaDailyRankRewardTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[ArenaDailyRankRewardTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[ArenaDailyRankRewardTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [ArenaDailyRankRewardTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *ArenaDailyRankRewardTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[ArenaDailyRankRewardTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [ArenaDailyRankRewardTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *ArenaDailyRankRewardTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[ArenaDailyRankRewardTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *ArenaDailyRankRewardTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[ArenaDailyRankRewardTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[ArenaDailyRankRewardTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
