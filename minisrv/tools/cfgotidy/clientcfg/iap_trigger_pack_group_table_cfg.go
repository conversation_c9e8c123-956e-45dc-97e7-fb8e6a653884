// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type IapTriggerPackGroupTableCfg struct {
	Id              int32           `json:"Id"`              // Id
	StringId        string          `json:"StringId"`        // StringId
	CountDown       int32           `json:"CountDown"`       // 限时/小时
	TriggerInterval int32           `json:"TriggerInterval"` // 每几个自然日限制触发1次
	TriggerPackType TriggerPackType `json:"TriggerPackType"` // 触发类型
	Formula         string          `json:"Formula"`         // 条件
	Value           float32         `json:"Value"`           // 值
	Title           string          `json:"Title"`           // 标题
	ImageTitle      string          `json:"ImageTitle"`      // 背景图-头
	Image           string          `json:"Image"`           // 背景图
	Thumbnail       string          `json:"Thumbnail"`       // 缩略图
}

func NewIapTriggerPackGroupTableCfg() *IapTriggerPackGroupTableCfg {
	return &IapTriggerPackGroupTableCfg{
		Id:              0,
		StringId:        "",
		CountDown:       0,
		TriggerInterval: 0,
		TriggerPackType: TriggerPackType(enumDefaultValue),
		Formula:         "",
		Value:           0.0,
		Title:           "",
		ImageTitle:      "",
		Image:           "",
		Thumbnail:       "",
	}
}

func NewMockIapTriggerPackGroupTableCfg() *IapTriggerPackGroupTableCfg {
	return &IapTriggerPackGroupTableCfg{
		Id:              0,
		StringId:        "",
		CountDown:       0,
		TriggerInterval: 0,
		TriggerPackType: TriggerPackType(enumDefaultValue),
		Formula:         "",
		Value:           0.0,
		Title:           "",
		ImageTitle:      "",
		Image:           "",
		Thumbnail:       "",
	}
}

type IapTriggerPackGroupTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*IapTriggerPackGroupTableCfg
	localIds         map[int32]struct{}
}

func NewIapTriggerPackGroupTable(configs *Configs) *IapTriggerPackGroupTable {
	return &IapTriggerPackGroupTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*IapTriggerPackGroupTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *IapTriggerPackGroupTable) Get(key int32) *IapTriggerPackGroupTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapTriggerPackGroupTable) GetAll() map[int32]*IapTriggerPackGroupTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapTriggerPackGroupTable) put(key int32, value *IapTriggerPackGroupTableCfg, local bool) *IapTriggerPackGroupTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *IapTriggerPackGroupTable) putFromInheritedTable(key int32, value *IapTriggerPackGroupTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[IapTriggerPackGroupTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapTriggerPackGroupTable) Put(key int32, value *IapTriggerPackGroupTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[IapTriggerPackGroupTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapTriggerPackGroupTable) PutAll(m map[int32]*IapTriggerPackGroupTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapTriggerPackGroupTable) Range(f func(v *IapTriggerPackGroupTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapTriggerPackGroupTable) Filter(filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) map[int32]*IapTriggerPackGroupTableCfg {
	filtered := map[int32]*IapTriggerPackGroupTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapTriggerPackGroupTable) FilterSlice(filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) []*IapTriggerPackGroupTableCfg {
	filtered := []*IapTriggerPackGroupTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapTriggerPackGroupTable) FilterKeys(filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapTriggerPackGroupTable) satisfied(v *IapTriggerPackGroupTableCfg, filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapTriggerPackGroupTable) setupIndexes() error {
	return nil
}

func (t *IapTriggerPackGroupTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapTriggerPackGroupTableCfg) bindRefs(c *Configs) {
}

func (t *IapTriggerPackGroupTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewIapTriggerPackGroupTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapTriggerPackGroupTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// CountDown
		{
			if record[t.getIndexInCsv("CountDown")] == "" {
				recordCfg.CountDown = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("CountDown")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapTriggerPackGroupTable]unmarshal csv record failed, varName=CountDown, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("CountDown")], err)
					} else {
						return fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv record failed, varName=CountDown, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("CountDown")], err)
					}
				}
				recordCfg.CountDown = int32(cfgoInt)
			}
		}
		// TriggerInterval
		{
			if record[t.getIndexInCsv("TriggerInterval")] == "" {
				recordCfg.TriggerInterval = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("TriggerInterval")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapTriggerPackGroupTable]unmarshal csv record failed, varName=TriggerInterval, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("TriggerInterval")], err)
					} else {
						return fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv record failed, varName=TriggerInterval, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("TriggerInterval")], err)
					}
				}
				recordCfg.TriggerInterval = int32(cfgoInt)
			}
		}
		// TriggerPackType
		{
			if record[t.getIndexInCsv("TriggerPackType")] == "" {
				recordCfg.TriggerPackType = TriggerPackType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTriggerPackType(record[t.getIndexInCsv("TriggerPackType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapTriggerPackGroupTable]unmarshal csv record failed, varName=TriggerPackType, type=enum@TriggerPackType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TriggerPackType")], err)
					} else {
						return fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv record failed, varName=TriggerPackType, type=enum@TriggerPackType, value=%s, err:[%s]", record[t.getIndexInCsv("TriggerPackType")], err)
					}
				}
				recordCfg.TriggerPackType = cfgoEnum
			}
		}
		// Formula
		{
			recordCfg.Formula = strings.TrimSpace(record[t.getIndexInCsv("Formula")])
		}
		// Value
		{
			if record[t.getIndexInCsv("Value")] == "" {
				recordCfg.Value = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Value")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapTriggerPackGroupTable]unmarshal csv record failed, varName=Value, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("Value")], err)
					} else {
						return fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv record failed, varName=Value, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("Value")], err)
					}
				}
				recordCfg.Value = float32(cfgoFloat)
			}
		}
		// Title
		{
			recordCfg.Title = strings.TrimSpace(record[t.getIndexInCsv("Title")])
		}
		// ImageTitle
		{
			recordCfg.ImageTitle = strings.TrimSpace(record[t.getIndexInCsv("ImageTitle")])
		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// Thumbnail
		{
			recordCfg.Thumbnail = strings.TrimSpace(record[t.getIndexInCsv("Thumbnail")])
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [IapTriggerPackGroupTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *IapTriggerPackGroupTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "IapTriggerPackGroupTable.csv") && (!strings.HasPrefix(fileName, "IapTriggerPackGroupTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for IapTriggerPackGroupTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[IapTriggerPackGroupTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[IapTriggerPackGroupTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[IapTriggerPackGroupTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[IapTriggerPackGroupTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[IapTriggerPackGroupTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[IapTriggerPackGroupTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[IapTriggerPackGroupTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[IapTriggerPackGroupTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [IapTriggerPackGroupTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *IapTriggerPackGroupTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[IapTriggerPackGroupTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [IapTriggerPackGroupTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *IapTriggerPackGroupTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[IapTriggerPackGroupTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *IapTriggerPackGroupTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[IapTriggerPackGroupTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[IapTriggerPackGroupTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
