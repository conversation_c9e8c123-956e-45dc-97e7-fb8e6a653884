// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t VipTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>lice(func(v *VipTableCfg) bool {
		return true
	}))
}

func (t VipTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*VipTableCfg) error {
	jsonPath := filepath.Join(dir, "VipTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[VipTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoVipTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoVipTableSlice []*VipTableCfg

func (x cfgoVipTableSlice) Len() int           { return len(x) }
func (x cfgoVipTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoVipTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *VipTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[VipTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*VipTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[VipTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[VipTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[VipTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t VipTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t VipTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(VipTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var VipTableJsonContent string = `{
		"FileName": "VipTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "VipTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "StringId",
				"FieldType": "string"
			},
			{
				"FieldName": "Level",
				"FieldType": "int32"
			},
			{
				"FieldName": "Max",
				"FieldType": "bool"
			},
			{
				"FieldName": "Exp",
				"FieldType": "int32"
			},
			{
				"FieldName": "Image",
				"FieldType": "string"
			},
			{
				"FieldName": "Title",
				"FieldType": "string"
			},
			{
				"FieldName": "Benefits",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "BenefitsValue",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Desc",
				"FieldType": "[DescStr]"
			},
			{
				"FieldName": "FreeGift",
				"FieldType": "[RewardKVS]"
			},
			{
				"FieldName": "FreeBg",
				"FieldType": "string"
			},
			{
				"FieldName": "FreeIcon",
				"FieldType": "string"
			},
			{
				"FieldName": "IapPackageId",
				"FieldType": "int32"
			},
			{
				"FieldName": "Limit",
				"FieldType": "PurchaseLimitType = DailyLimit"
			},
			{
				"FieldName": "Times",
				"FieldType": "int32"
			}
		]
	}`
