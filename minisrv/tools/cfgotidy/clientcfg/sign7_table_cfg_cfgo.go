// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t Sign7Table) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *Sign7TableCfg) bool {
		return true
	}))
}

func (t Sign7Table) SaveJsonWithSuffix(dir string, suffix string, cfgs []*Sign7TableCfg) error {
	jsonPath := filepath.Join(dir, "Sign7Table"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[Sign7Table]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoSign7TableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoSign7TableSlice []*Sign7TableCfg

func (x cfgoSign7TableSlice) Len() int           { return len(x) }
func (x cfgoSign7TableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoSign7TableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *Sign7Table) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[Sign7Table]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*Sign7TableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[Sign7Table]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[Sign7Table]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[Sign7Table]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t Sign7Table) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t Sign7Table) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(Sign7TableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var Sign7TableJsonContent string = `{
		"FileName": "Sign7Table",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "Sign7Table",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "StringId",
				"FieldType": "string"
			},
			{
				"FieldName": "Turn",
				"FieldType": "int32"
			},
			{
				"FieldName": "Day",
				"FieldType": "int32"
			},
			{
				"FieldName": "Reward",
				"FieldType": "[RewardKVS]"
			},
			{
				"FieldName": "Image",
				"FieldType": "string"
			}
		]
	}`
