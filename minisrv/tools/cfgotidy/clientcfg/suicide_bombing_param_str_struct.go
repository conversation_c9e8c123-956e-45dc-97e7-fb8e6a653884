// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type SuicideBombingParamStr struct {
	Target     MissileTarget     `json:"Target"`     // 目标
	DmgRatio   float32           `json:"DmgRatio"`   // 伤害系数
	RangeParam *CircularRangeStr `json:"RangeParam"` // 范围参数
}

func NewSuicideBombingParamStr() *SuicideBombingParamStr {
	return &SuicideBombingParamStr{
		Target:     MissileTarget(enumDefaultValue),
		DmgRatio:   0.0,
		RangeParam: NewCircularRangeStr(),
	}
}

func NewMockSuicideBombingParamStr() *SuicideBombingParamStr {
	return &SuicideBombingParamStr{
		Target:     MissileTarget(enumDefaultValue),
		DmgRatio:   0.0,
		RangeParam: NewMockCircularRangeStr(),
	}
}

func (s *SuicideBombingParamStr) bindRefs(c *Configs) {
	s.RangeParam.bindRefs(c)
}
