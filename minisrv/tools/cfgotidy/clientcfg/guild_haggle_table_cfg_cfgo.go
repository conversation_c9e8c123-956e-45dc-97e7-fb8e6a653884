// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t GuildHaggleTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *GuildHaggleTableCfg) bool {
		return true
	}))
}

func (t GuildHaggleTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*GuildHaggleTableCfg) error {
	jsonPath := filepath.Join(dir, "GuildHaggleTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[GuildHaggleTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoGuildHaggleTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoGuildHaggleTableSlice []*GuildHaggleTableCfg

func (x cfgoGuildHaggleTableSlice) Len() int           { return len(x) }
func (x cfgoGuildHaggleTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoGuildHaggleTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *GuildHaggleTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[GuildHaggleTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*GuildHaggleTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[GuildHaggleTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[GuildHaggleTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[GuildHaggleTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t GuildHaggleTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t GuildHaggleTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(GuildHaggleTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var GuildHaggleTableJsonContent string = `{
		"FileName": "GuildHaggleTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "GuildHaggleTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "Reward",
				"FieldType": "[RewardKVS]"
			},
			{
				"FieldName": "RewardCost",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "HaggleLeast",
				"FieldType": "int32"
			},
			{
				"FieldName": "HaggleMag",
				"FieldType": "int32"
			},
			{
				"FieldName": "HaggleRate",
				"FieldType": "int32"
			},
			{
				"FieldName": "RewardType",
				"FieldType": "int32"
			},
			{
				"FieldName": "RewardValue",
				"FieldType": "int32"
			}
		]
	}`
