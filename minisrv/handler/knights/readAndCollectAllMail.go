package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/mail"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 删除所有已读邮件
func (_ KnightsHandlers) ReadAndCollectAllMailHandler(ctx *wctx.Context, req *wrpc.ReadAndCollectAllMailRequest) (*wrpc.ReadAndCollectAllMailReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret, e := mail.ReadAndCollectAllMail(ctx, req.UID)
		rawResp = &wrpc.ReadAndCollectAllMailReply{Result: ret}
		err = e
	})
	if rawResp != nil {
		return rawResp.(*wrpc.ReadAndCollectAllMailReply), err
	}
	return nil, nil
}
