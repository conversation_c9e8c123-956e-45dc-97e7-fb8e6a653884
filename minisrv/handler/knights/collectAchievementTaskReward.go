package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 领取成就任务奖励
func (_ KnightsHandlers) CollectAchievementTaskRewardHandler(ctx *wctx.Context, req *wrpc.CollectAchievementTaskRewardRequest) (*wrpc.CollectAchievementTaskRewardReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		rewards, e := quest.CollectAchievementReward(ctx, req.UID, req.QuestId)
		err = e
		rawResp = &wrpc.CollectAchievementTaskRewardReply{Result: rewards}
	})
	if rawResp != nil {
		return rawResp.(*wrpc.CollectAchievementTaskRewardReply), nil
	}
	return nil, err
}
