package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/mail"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 获取邮件列表
func (_ KnightsHandlers) GetMailListHandler(ctx *wctx.Context, req *wrpc.GetMailListRequest) (*wrpc.GetMailListReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		GetMailList := mail.GetMailList(ctx, req.UID)

		rawResp = &wrpc.GetMailListReply{Result: &wrpc.GetMailListResult{
			MailList: GetMailList,
		}}
	})
	return rawResp.(*wrpc.GetMailListReply), err
}
