package wrpc

@pb import "pb1.proto";


// {{FuncName}} [{{FuncNumber}}]...
SC void PushMsgs(uint32 msgType, int64 msgId, string msg) = 6001

// {{FuncName}} [{{FuncNumber}}]...
TP void PushTopicMsg(uint32 msgType, string msg) = 6002


// {{FuncName}} [{{FuncNumber}}]...
CS (uint64[] uids) GetOnlineUsers(int64[] uids) = 6003


// {{FuncName}} [{{FuncNumber}}]...
SC void PushPBMsgs(uint32 msgType, int64 msgId, bytes msg) = 6004

TP void PushPBTopicMsg(uint32 msgType, bytes msg) = 6005

