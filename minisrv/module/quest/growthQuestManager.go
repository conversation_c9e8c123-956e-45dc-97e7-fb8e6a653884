package quest

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
)

func unlockGrowthQuest(ctx context.Context, uid int64, buildId int32) {
	//lines := cfg_mgr.Cfg.GrowthTasksGroupTable.FilterSlice(func(v *servercfg.GrowthTasksGroupTableCfg) bool {
	//	return v.ReqBuildingsID == buildId
	//})
	//if lines == nil {
	//	return
	//}
	//for _, line := range lines {
	//	GenGrowthQuest(ctx, uid, line.Id)
	//}

}

func GenGrowthQuest(ctx context.Context, uid int64, groupId int32) error {
	//growthTaskLines := cfg_mgr.Cfg.GrowthTasksMainTable.Filter(func(v *servercfg.GrowthTasksMainTableCfg) bool {
	//	return groupId == v.GrowthTasksGroup
	//})
	//for _, v := range growthTaskLines {
	//	quests, err := orm.Get[*model.GrowthTask](ctx, v.Id)
	//	if err != nil {
	//		return kdmerr.SysDBError.WrapError(err)
	//	}
	//	if quests != nil {
	//		continue
	//	}
	//	orm.Create[*model.GrowthTask](ctx, &minirpc.GrowthTask{
	//		Uid:      uid,
	//		QuestId:  v.Id,
	//		Type:     int32(v.TaskType),
	//		Progress: 0,
	//		Status:   int32(minirpc.QuestActivity),
	//		GroupId:  v.GrowthTasksGroup,
	//	})
	//}
	return nil
}

func CollectGrowthReward(ctx context.Context, uid int64, questId int32) ([]*wrpc.Rewards, error) {
	//quest, err := model.GetGrowthTaskModel(ctx, uid, questId)
	//if err != nil {
	//	return nil, kdmerr.SysDBError.WrapError(err)
	//}
	//if quest == nil || quest.Status() != int32(minirpc.QuestComplete) {
	//	return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is error")
	//}
	//line := cfg_mgr.Cfg.GrowthTasksMainTable.Get(questId)
	//if line == nil {
	//	return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is error")
	//}
	//quest.SetStatus(ctx, int32(minirpc.QuestRewarded))
	//l := len(line.RewardType)
	//var rewards []*wrpc.Rewards
	//for i := 0; i < l; i++ {
	//	model.AddItem(ctx, uid, line.RewardType[i], int64(line.RewardValue[i]))
	//	rewards = append(rewards, &wrpc.Rewards{
	//		ItemId: line.RewardType[i], ItemValue: int64(line.RewardValue[i]),
	//	})
	//}
	//
	//return rewards, nil
	return nil, nil
}
