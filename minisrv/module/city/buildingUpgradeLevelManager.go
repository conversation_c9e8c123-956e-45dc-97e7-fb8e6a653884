package city

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/enum"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
)

func (b *BuildingUpgradeLevel) OnTimeOut(ctx context.Context, job *model.UserJob) {
	orm.Lock(job)
	defer orm.Unlock(job)
	trace := job.GetTrace()
	buildingId := trace[enum.BuildId]
	OnUpgradeFinish(ctx, job.Uid(), buildingId)
	job.Delete(ctx)
}

type BuildingUpgradeLevel struct {
	*model.BaseWorker
}
