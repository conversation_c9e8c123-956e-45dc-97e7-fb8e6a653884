package workque

import (
	"context"
	"gitlab-ee.funplus.io/watcher/watcher/misc/deque"
	"gitlab-ee.funplus.io/watcher/watcher/misc/wlog"
	"runtime/debug"
	"sync"
	"time"
)

const defaultBatchSize = 64

type WorkQue struct {
	once        sync.Once
	cv          sync.Cond
	workQueue   deque.Deque
	batchSize   int
	workHandler func(cmd interface{})
}

func NewWorkQue(option ...workQueOption) *WorkQue {
	w := &WorkQue{
		cv:        sync.Cond{L: &sync.Mutex{}},
		workQueue: deque.NewDeque(),
		batchSize: defaultBatchSize,
		workHandler: func(cmd interface{}) {
			// nothing
		},
	}
	for _, op := range option {
		op(w)
	}
	return w
}

func (this *WorkQue) Launch() {
	this.once.Do(func() {
		go this.engine()
	})
}

func (this *WorkQue) engine() {
	defer func() {
		if r := recover(); r != nil {
			wlog.Errorf("<activityManager.engine> panic: %+v\n%s", r, debug.Stack())
			time.Sleep(time.Second)
			go this.engine()
		}
	}()
	var st *shutdown
loop:
	for {
		this.cv.L.Lock()
		var xCmds = this.workQueue.DequeueMany(this.batchSize)
		for ; len(xCmds) == 0; xCmds = this.workQueue.DequeueMany(this.batchSize) {
			this.cv.Wait()
		}
		this.cv.L.Unlock()
		for _, xCmd := range xCmds {
			switch c := xCmd.(type) {
			case *shutdown:
				st = c
			default:
				this.workHandler(xCmd)
			}
		}
		if st != nil {
			break loop
		}
	}

	// shutdown
	if st != nil {
		this.shutdownImpl(st)
	}
}

func (this *WorkQue) shutdownImpl(st *shutdown) {
	round := 0
	for {
		this.cv.L.Lock()
		xCmds := this.workQueue.DequeueMany(this.batchSize)
		this.cv.L.Unlock()
		if len(xCmds) == 0 {
			break
		}
		round++
		if round > 1 { // slow loop
			const nap = time.Millisecond * 5
			time.Sleep(nap)
		}
		select {
		case <-st.ctx.Done():
			st.err <- st.ctx.Err()
			return
		default:
			for _, xCmd := range xCmds {
				switch xCmd.(type) {
				case *shutdown:
				default:
					this.workHandler(xCmd)
				}
			}
		}
	}
	st.err <- nil
}

func (this *WorkQue) AddCmd(cmd any) {
	this.cv.L.Lock()
	this.workQueue.Enqueue(cmd)
	this.cv.Signal()
	this.cv.L.Unlock()
}

// Shutdown wait shutdown
func (this *WorkQue) Shutdown(ctx context.Context) error {
	return this.shutdownAsyncImpl(ctx).Err()
}

func (this *WorkQue) shutdownAsyncImpl(ctx context.Context) *invocationPod {
	if ctx == nil {
		ctx1, cancel := context.WithTimeout(context.TODO(), 5*time.Second)
		defer func() {
			cancel()
		}()
		ctx = ctx1
	}
	st := &shutdown{
		ctx: ctx,
		err: make(chan error, 1),
	}
	ret := st.err
	this.AddCmd(st)
	return &invocationPod{
		err: ret,
	}
}

type invocationPod struct {
	err chan error
}

func (this *invocationPod) Err() error {
	return <-this.err
}

type shutdown struct {
	ctx context.Context
	err chan error
}

type workQueOption func(que *WorkQue)

func WithWorkHandler(f func(cmd interface{})) workQueOption {
	return func(que *WorkQue) {
		que.workHandler = f
	}
}

func WithBatchSize(size int) workQueOption {
	return func(que *WorkQue) {
		que.batchSize = size
	}
}
