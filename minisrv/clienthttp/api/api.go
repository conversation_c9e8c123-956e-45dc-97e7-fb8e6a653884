package api

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/srvconf"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"encoding/json"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

// RegisterRoutes 注册客户端API路由
func RegisterRoutes(router *httprouter.Router) {

	// Manifest API
	router.POST("/api/manifest", handleGetManifest)
	router.GET("/api/manifest", handleGetManifestGET)
}

// 通用JSON响应函数
func jsonResponse(w http.ResponseWriter, statusCode int, message string, data interface{}) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"code":    statusCode,
		"message": message,
	}

	if data != nil {
		response["data"] = data
	}

	json.NewEncoder(w).Encode(response)
}

// handleGetManifest 处理POST方式的Manifest请求
func handleGetManifest(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req struct {
		OS            string `json:"os"`
		ClientVersion string `json:"client_version"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	// 调用内部处理函数
	handleManifestRequest(w, req.OS, req.ClientVersion)
}

// handleGetManifestGET 处理GET方式的Manifest请求
func handleGetManifestGET(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	os := r.URL.Query().Get("os")
	clientVersion := r.URL.Query().Get("client_version")

	// 调用内部处理函数
	handleManifestRequest(w, os, clientVersion)
}

// handleManifestRequest 内部Manifest处理函数
func handleManifestRequest(w http.ResponseWriter, os, clientVersion string) {
	var manifest *wrpc.ManiFest
	var err error

	tasklet.Invoke(context.Background(), "handleManifestRequest", func(ctx context.Context) {
		// 标准化参数
		if os == "android" {
			os = "android"
		} else {
			os = "ios"
		}
		if clientVersion == "" {
			clientVersion = "1.0.0"
		}

		// 获取配置和版本信息
		kdmSrvConfig := srvconf.GetKdmSrvConfig()
		clientVersionModel, _ := model.GetClientVersionModel(ctx, os, clientVersion)
		if clientVersionModel != nil {
			err = fmt.Errorf("client version not found")
			return
		}

		// 获取CDN配置
		cdn := ""
		if kdmSrvConfig != nil && kdmSrvConfig.KdmsrvConf.ClientConfig != nil {
			cdn = kdmSrvConfig.KdmsrvConf.ClientConfig.Cdn
		}

		// 构建manifest
		manifest = &wrpc.ManiFest{
			LangUrl:        "http://gog-global-cdn.akamaized.net/gogx/language/guardian/1.0.0/1/language_zh-CN.json",
			AppId:          " gogx.global.prod",
			BundleVersionR: clientVersionModel.BundleVersionR(),
			BundleVersionG: clientVersionModel.BundleVersionG(),
			Cdn:            cdn,
		}

		// 获取华佗版本信息
		huoTuoVersion, _ := model.GetHuatuoVersionModel(ctx, os, clientVersion)
		if huoTuoVersion != nil {
			manifest.Version = huoTuoVersion.ClientVersion()
			manifest.TargetVersion = huoTuoVersion.TargetVersion()
			manifest.BuildJob = huoTuoVersion.JobId()
		}
	})

	if err != nil {
		jsonResponse(w, http.StatusBadRequest, err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "ok", manifest)
}
