package api

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/srvconf"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"encoding/json"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

// GenericAPIRequest 通用API请求结构
type GenericAPIRequest struct {
	Class  string                 `json:"class"`
	Method string                 `json:"method"`
	Params map[string]interface{} `json:"params"`
}

// RegisterRoutes 注册客户端API路由
func RegisterRoutes(router *httprouter.Router) {

	// 通用API路由 - 支持 class/method/params 格式
	router.POST("/api/", handleGenericAPI)

	// Manifest API
	router.POST("/api/manifest", handleGetManifest)
	router.GET("/api/manifest", handleGetManifestGET)
}

// 通用JSON响应函数
func jsonResponse(w http.ResponseWriter, statusCode int, message string, data interface{}) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"code":    statusCode,
		"message": message,
	}

	if data != nil {
		response["data"] = data
	}

	json.NewEncoder(w).Encode(response)
}

// handleGetManifest 处理POST方式的Manifest请求
func handleGetManifest(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req struct {
		OS            string `json:"os"`
		ClientVersion string `json:"client_version"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	// 调用内部处理函数
	handleManifestRequest(w, req.OS, req.ClientVersion)
}

// handleGetManifestGET 处理GET方式的Manifest请求
func handleGetManifestGET(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	os := r.URL.Query().Get("os")
	clientVersion := r.URL.Query().Get("client_version")

	// 调用内部处理函数
	handleManifestRequest(w, os, clientVersion)
}

// handleManifestRequest 内部Manifest处理函数
func handleManifestRequest(w http.ResponseWriter, os, clientVersion string) {
	var manifest *wrpc.ManiFest
	var err error

	tasklet.Invoke(context.Background(), "handleManifestRequest", func(ctx context.Context) {
		// 标准化参数
		if os == "android" {
			os = "android"
		} else {
			os = "ios"
		}
		if clientVersion == "" {
			clientVersion = "1.0.0"
		}

		// 获取配置和版本信息
		kdmSrvConfig := srvconf.GetKdmSrvConfig()

		// 获取CDN配置
		cdn := ""
		if kdmSrvConfig != nil && kdmSrvConfig.KdmsrvConf.ClientConfig != nil {
			cdn = kdmSrvConfig.KdmsrvConf.ClientConfig.Cdn
		}

		// 构建manifest
		manifest = &wrpc.ManiFest{
			LangUrl: "http://gog-global-cdn.akamaized.net/gogx/language/guardian/1.0.0/1/language_zh-CN.json",
			AppId:   " gogx.global.prod",

			Cdn: cdn,
		}

		clientVersionModel, _ := model.GetClientVersionModel(ctx, os, clientVersion)
		if clientVersionModel != nil {

			manifest.BundleVersionR = clientVersionModel.BundleVersionR()
			manifest.BundleVersionG = clientVersionModel.BundleVersionG()
		}

		// 获取华佗版本信息
		huoTuoVersion, _ := model.GetHuatuoVersionModel(ctx, os, clientVersion)
		if huoTuoVersion != nil {
			manifest.Version = huoTuoVersion.ClientVersion()
			manifest.TargetVersion = huoTuoVersion.TargetVersion()
			manifest.BuildJob = huoTuoVersion.JobId()
		}
	})

	if err != nil {
		jsonResponse(w, http.StatusBadRequest, err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "ok", manifest)
}

// handleGenericAPI 处理通用API请求
func handleGenericAPI(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req GenericAPIRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid JSON request body", nil)
		return
	}

	// 根据class和method路由到相应的处理函数
	switch req.Class {
	case "call":
		switch req.Method {
		case "manifest":
			handleManifestFromGeneric(w, req.Params)
		case "versionDiff2":
			handleVersionDiff2FromGeneric(w, req.Params)
		default:
			jsonResponse(w, http.StatusBadRequest, "Unsupported method: "+req.Method, nil)
		}
	default:
		jsonResponse(w, http.StatusBadRequest, "Unsupported class: "+req.Class, nil)
	}
}

// handleManifestFromGeneric 从通用API请求中处理manifest请求
func handleManifestFromGeneric(w http.ResponseWriter, params map[string]interface{}) {
	// 从 params 中提取参数
	os := getStringParam(params, "os")
	clientVersion := getStringParam(params, "version") // 注意：请求中使用的是 "version"，不是 "client_version"
	channel := getStringParam(params, "channel")       // 获取channel参数（可选）

	// 记录channel信息（如果需要的话）
	_ = channel // 目前不使用，但保留以便未来扩展

	// 调用内部处理函数
	handleManifestRequest(w, os, clientVersion)
}
