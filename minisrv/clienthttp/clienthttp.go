package clienthttp

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/clienthttp/api"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/mg-server/mgsl/log"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
)

var (
	clientHttpSrv  *http.Server
	ClientHttpPort = 0
	clientRouter   = httprouter.New()
)

func panicHandler(w http.ResponseWriter, r *http.Request, err interface{}) {
	msg := fmt.Sprintf("<clienthttp> panic: %+v\n%s", err, debug.Stack())
	_, _ = fmt.Fprintf(w, `{"err": %q}`, msg)
	log.Error(msg)
}

func initRouter() {
	clientRouter.PanicHandler = panicHandler

	// 注册客户端API路由
	api.RegisterRoutes(clientRouter)

	// 添加健康检查端点
	clientRouter.GET("/health", handleHealth)
	clientRouter.GET("/", handleRoot)

	// 添加CORS支持
	clientRouter.GlobalOPTIONS = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Access-Control-Request-Method") != "" {
			// 预检请求
			header := w.Header()
			header.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			header.Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		}

		// 设置CORS头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.WriteHeader(http.StatusNoContent)
	})
}

func handleHealth(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, _ = fmt.Fprintf(w, `{"status": "ok", "service": "client-http", "port": %d}`, ClientHttpPort)
}

func handleRoot(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, _ = fmt.Fprintf(w, `{"message": "GoG Knights Client HTTP Server", "version": "1.0.0", "port": %d}`, ClientHttpPort)
}

func ServeClientHttp(addr string) error {
	clientHttpSrv = &http.Server{Addr: addr, Handler: clientRouter}
	log.Infow("<clienthttp> listening on " + addr)

	// convert http listen port from string to int
	port, err := strconv.Atoi(strings.TrimLeft(addr, ":"))
	if err != nil {
		return kdmerr.SysInvalidArguments.WrapError(err)
	}
	ClientHttpPort = port

	// start http server
	if err := clientHttpSrv.ListenAndServe(); err != nil {
		return kdmerr.SysInvalidArguments.WrapError(err)
	}

	return nil
}

func init() {
	initRouter()
}
