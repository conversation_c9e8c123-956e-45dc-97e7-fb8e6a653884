// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type LordGemDropCntTableCfg struct {
	Id               int32         `json:"Id"`               // Id
	LordGemRandom    int32         `json:"LordGemRandom"`    // 随机宝石
	LordGemRandomRef *ItemTableCfg `json:"-"`                // 随机宝石
	LordGemCnt       int32         `json:"LordGemCnt"`       // 宝石数量
	LordGemCntWeight int32         `json:"LordGemCntWeight"` // 数量权重
}

func NewLordGemDropCntTableCfg() *LordGemDropCntTableCfg {
	return &LordGemDropCntTableCfg{
		Id:               0,
		LordGemRandom:    0,
		LordGemRandomRef: nil,
		LordGemCnt:       0,
		LordGemCntWeight: 0,
	}
}

type LordGemDropCntTable struct {
	records  map[int32]*LordGemDropCntTableCfg
	localIds map[int32]struct{}
}

func NewLordGemDropCntTable() *LordGemDropCntTable {
	return &LordGemDropCntTable{
		records:  map[int32]*LordGemDropCntTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *LordGemDropCntTable) Get(key int32) *LordGemDropCntTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LordGemDropCntTable) GetAll() map[int32]*LordGemDropCntTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LordGemDropCntTable) put(key int32, value *LordGemDropCntTableCfg, local bool) *LordGemDropCntTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LordGemDropCntTable) Range(f func(v *LordGemDropCntTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LordGemDropCntTable) Filter(filterFuncs ...func(v *LordGemDropCntTableCfg) bool) map[int32]*LordGemDropCntTableCfg {
	filtered := map[int32]*LordGemDropCntTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LordGemDropCntTable) FilterSlice(filterFuncs ...func(v *LordGemDropCntTableCfg) bool) []*LordGemDropCntTableCfg {
	filtered := []*LordGemDropCntTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LordGemDropCntTable) FilterKeys(filterFuncs ...func(v *LordGemDropCntTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LordGemDropCntTable) satisfied(v *LordGemDropCntTableCfg, filterFuncs ...func(v *LordGemDropCntTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LordGemDropCntTable) setupIndexes() error {
	return nil
}

func (t *LordGemDropCntTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LordGemDropCntTableCfg) bindRefs(c *Configs) {
	r.LordGemRandomRef = c.ItemTable.Get(r.LordGemRandom)
}
