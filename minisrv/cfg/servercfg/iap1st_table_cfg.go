// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type Iap1stTableCfg struct {
	Id              int32               `json:"Id"`           // Id
	StringId        string              `json:"StringId"`     // StringId
	IapPackageId    int32               `json:"IapPackageId"` // 内购商品id
	IapPackageIdRef *IapPackageTableCfg `json:"-"`            // 内购商品id
	Priority        int32               `json:"Priority"`     // 优先级（小在前）
	Limit           PurchaseLimitType   `json:"Limit"`        // 限购类型
	Times           int32               `json:"Times"`        // 限购次数
	D2              []*RewardKVS        `json:"D2"`           // 第2天奖励
	D3              []*RewardKVS        `json:"D3"`           // 第3天奖励
}

func NewIap1stTableCfg() *Iap1stTableCfg {
	return &Iap1stTableCfg{
		Id:              0,
		StringId:        "",
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Priority:        0,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
		D2:              []*RewardKVS{},
		D3:              []*RewardKVS{},
	}
}

type Iap1stTable struct {
	records  map[int32]*Iap1stTableCfg
	localIds map[int32]struct{}
}

func NewIap1stTable() *Iap1stTable {
	return &Iap1stTable{
		records:  map[int32]*Iap1stTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *Iap1stTable) Get(key int32) *Iap1stTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *Iap1stTable) GetAll() map[int32]*Iap1stTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *Iap1stTable) put(key int32, value *Iap1stTableCfg, local bool) *Iap1stTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *Iap1stTable) Range(f func(v *Iap1stTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *Iap1stTable) Filter(filterFuncs ...func(v *Iap1stTableCfg) bool) map[int32]*Iap1stTableCfg {
	filtered := map[int32]*Iap1stTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *Iap1stTable) FilterSlice(filterFuncs ...func(v *Iap1stTableCfg) bool) []*Iap1stTableCfg {
	filtered := []*Iap1stTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *Iap1stTable) FilterKeys(filterFuncs ...func(v *Iap1stTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *Iap1stTable) satisfied(v *Iap1stTableCfg, filterFuncs ...func(v *Iap1stTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *Iap1stTable) setupIndexes() error {
	return nil
}

func (t *Iap1stTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *Iap1stTableCfg) bindRefs(c *Configs) {
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
	for _, e := range r.D2 {
		e.bindRefs(c)
	}
	for _, e := range r.D3 {
		e.bindRefs(c)
	}
}
