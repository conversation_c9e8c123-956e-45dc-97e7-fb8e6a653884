// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type AvatarTableCfg struct {
	Id      int32         `json:"Id"`      // Id
	Default bool          `json:"Default"` // 是否默认
	Hero    int32         `json:"Hero"`    // 对应英雄
	HeroRef *HeroTableCfg `json:"-"`       // 对应英雄
	Image   string        `json:"Image"`   // 头像图标
	Name    string        `json:"Name"`    // 头像名
}

func NewAvatarTableCfg() *AvatarTableCfg {
	return &AvatarTableCfg{
		Id:      0,
		Default: false,
		Hero:    0,
		HeroRef: nil,
		Image:   "",
		Name:    "",
	}
}

type AvatarTable struct {
	records  map[int32]*AvatarTableCfg
	localIds map[int32]struct{}
}

func NewAvatarTable() *AvatarTable {
	return &AvatarTable{
		records:  map[int32]*AvatarTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *AvatarTable) Get(key int32) *AvatarTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *AvatarTable) GetAll() map[int32]*AvatarTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *AvatarTable) put(key int32, value *AvatarTableCfg, local bool) *AvatarTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *AvatarTable) Range(f func(v *AvatarTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *AvatarTable) Filter(filterFuncs ...func(v *AvatarTableCfg) bool) map[int32]*AvatarTableCfg {
	filtered := map[int32]*AvatarTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *AvatarTable) FilterSlice(filterFuncs ...func(v *AvatarTableCfg) bool) []*AvatarTableCfg {
	filtered := []*AvatarTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *AvatarTable) FilterKeys(filterFuncs ...func(v *AvatarTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *AvatarTable) satisfied(v *AvatarTableCfg, filterFuncs ...func(v *AvatarTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *AvatarTable) setupIndexes() error {
	return nil
}

func (t *AvatarTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *AvatarTableCfg) bindRefs(c *Configs) {
	r.HeroRef = c.HeroTable.Get(r.Hero)
}
