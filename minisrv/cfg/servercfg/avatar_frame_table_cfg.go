// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type AvatarFrameTableCfg struct {
	Id       int32          `json:"Id"`       // Id
	Item     int32          `json:"Item"`     // 对应道具
	ItemRef  *ItemTableCfg  `json:"-"`        // 对应道具
	Benefits []*BenefitsKVS `json:"Benefits"` // 头像框增益
	Name     string         `json:"Name"`     // 头像框名
}

func NewAvatarFrameTableCfg() *AvatarFrameTableCfg {
	return &AvatarFrameTableCfg{
		Id:       0,
		Item:     0,
		ItemRef:  nil,
		Benefits: []*BenefitsKVS{},
		Name:     "",
	}
}

type AvatarFrameTable struct {
	records  map[int32]*AvatarFrameTableCfg
	localIds map[int32]struct{}
}

func NewAvatarFrameTable() *AvatarFrameTable {
	return &AvatarFrameTable{
		records:  map[int32]*AvatarFrameTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *AvatarFrameTable) Get(key int32) *AvatarFrameTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *AvatarFrameTable) GetAll() map[int32]*AvatarFrameTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *AvatarFrameTable) put(key int32, value *AvatarFrameTableCfg, local bool) *AvatarFrameTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *AvatarFrameTable) Range(f func(v *AvatarFrameTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *AvatarFrameTable) Filter(filterFuncs ...func(v *AvatarFrameTableCfg) bool) map[int32]*AvatarFrameTableCfg {
	filtered := map[int32]*AvatarFrameTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *AvatarFrameTable) FilterSlice(filterFuncs ...func(v *AvatarFrameTableCfg) bool) []*AvatarFrameTableCfg {
	filtered := []*AvatarFrameTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *AvatarFrameTable) FilterKeys(filterFuncs ...func(v *AvatarFrameTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *AvatarFrameTable) satisfied(v *AvatarFrameTableCfg, filterFuncs ...func(v *AvatarFrameTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *AvatarFrameTable) setupIndexes() error {
	return nil
}

func (t *AvatarFrameTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *AvatarFrameTableCfg) bindRefs(c *Configs) {
	r.ItemRef = c.ItemTable.Get(r.Item)
	for _, e := range r.Benefits {
		e.bindRefs(c)
	}
}
