// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type GuildTasksScoreTableCfg struct {
	Id            int32           `json:"Id"`          // Id
	Score         int32           `json:"Score"`       // 积分
	RewardType    []int32         `json:"RewardType"`  // 奖励类型
	RewardTypeRef []*ItemTableCfg `json:"-"`           // 奖励类型
	RewardValue   []int32         `json:"RewardValue"` // 奖励数量
}

func NewGuildTasksScoreTableCfg() *GuildTasksScoreTableCfg {
	return &GuildTasksScoreTableCfg{
		Id:            0,
		Score:         0,
		RewardType:    []int32{},
		RewardTypeRef: []*ItemTableCfg{},
		RewardValue:   []int32{},
	}
}

type GuildTasksScoreTable struct {
	records  map[int32]*GuildTasksScoreTableCfg
	localIds map[int32]struct{}
}

func NewGuildTasksScoreTable() *GuildTasksScoreTable {
	return &GuildTasksScoreTable{
		records:  map[int32]*GuildTasksScoreTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *GuildTasksScoreTable) Get(key int32) *GuildTasksScoreTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *GuildTasksScoreTable) GetAll() map[int32]*GuildTasksScoreTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *GuildTasksScoreTable) put(key int32, value *GuildTasksScoreTableCfg, local bool) *GuildTasksScoreTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *GuildTasksScoreTable) Range(f func(v *GuildTasksScoreTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *GuildTasksScoreTable) Filter(filterFuncs ...func(v *GuildTasksScoreTableCfg) bool) map[int32]*GuildTasksScoreTableCfg {
	filtered := map[int32]*GuildTasksScoreTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *GuildTasksScoreTable) FilterSlice(filterFuncs ...func(v *GuildTasksScoreTableCfg) bool) []*GuildTasksScoreTableCfg {
	filtered := []*GuildTasksScoreTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *GuildTasksScoreTable) FilterKeys(filterFuncs ...func(v *GuildTasksScoreTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *GuildTasksScoreTable) satisfied(v *GuildTasksScoreTableCfg, filterFuncs ...func(v *GuildTasksScoreTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *GuildTasksScoreTable) setupIndexes() error {
	return nil
}

func (t *GuildTasksScoreTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *GuildTasksScoreTableCfg) bindRefs(c *Configs) {
	for _, e := range r.RewardType {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.RewardTypeRef = append(r.RewardTypeRef, cfgoRefRecord)
	}
}
