// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapTriggerPackGroupTableCfg struct {
	Id              int32           `json:"Id"`              // Id
	StringId        string          `json:"StringId"`        // StringId
	CountDown       int32           `json:"CountDown"`       // 限时/小时
	TriggerInterval int32           `json:"TriggerInterval"` // 每几个自然日限制触发1次
	TriggerPackType TriggerPackType `json:"TriggerPackType"` // 触发类型
	Formula         string          `json:"Formula"`         // 条件
	Value           float32         `json:"Value"`           // 值
}

func NewIapTriggerPackGroupTableCfg() *IapTriggerPackGroupTableCfg {
	return &IapTriggerPackGroupTableCfg{
		Id:              0,
		StringId:        "",
		CountDown:       0,
		TriggerInterval: 0,
		TriggerPackType: TriggerPackType(enumDefaultValue),
		Formula:         "",
		Value:           0.0,
	}
}

type IapTriggerPackGroupTable struct {
	records  map[int32]*IapTriggerPackGroupTableCfg
	localIds map[int32]struct{}
}

func NewIapTriggerPackGroupTable() *IapTriggerPackGroupTable {
	return &IapTriggerPackGroupTable{
		records:  map[int32]*IapTriggerPackGroupTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapTriggerPackGroupTable) Get(key int32) *IapTriggerPackGroupTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapTriggerPackGroupTable) GetAll() map[int32]*IapTriggerPackGroupTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapTriggerPackGroupTable) put(key int32, value *IapTriggerPackGroupTableCfg, local bool) *IapTriggerPackGroupTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapTriggerPackGroupTable) Range(f func(v *IapTriggerPackGroupTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapTriggerPackGroupTable) Filter(filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) map[int32]*IapTriggerPackGroupTableCfg {
	filtered := map[int32]*IapTriggerPackGroupTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapTriggerPackGroupTable) FilterSlice(filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) []*IapTriggerPackGroupTableCfg {
	filtered := []*IapTriggerPackGroupTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapTriggerPackGroupTable) FilterKeys(filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapTriggerPackGroupTable) satisfied(v *IapTriggerPackGroupTableCfg, filterFuncs ...func(v *IapTriggerPackGroupTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapTriggerPackGroupTable) setupIndexes() error {
	return nil
}

func (t *IapTriggerPackGroupTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapTriggerPackGroupTableCfg) bindRefs(c *Configs) {
}
