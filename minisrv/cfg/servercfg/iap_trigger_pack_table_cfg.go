// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapTriggerPackTableCfg struct {
	Id              int32                        `json:"Id"`           // Id
	StringId        string                       `json:"StringId"`     // StringId
	Group           int32                        `json:"Group"`        // 所属礼包组
	GroupRef        *IapTriggerPackGroupTableCfg `json:"-"`            // 所属礼包组
	IapPackageId    int32                        `json:"IapPackageId"` // 内购商品id
	IapPackageIdRef *IapPackageTableCfg          `json:"-"`            // 内购商品id
	Limit           PurchaseLimitType            `json:"Limit"`        // 限购类型
	Times           int32                        `json:"Times"`        // 限购次数
}

func NewIapTriggerPackTableCfg() *IapTriggerPackTableCfg {
	return &IapTriggerPackTableCfg{
		Id:              0,
		StringId:        "",
		Group:           0,
		GroupRef:        nil,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

type IapTriggerPackTable struct {
	records  map[int32]*IapTriggerPackTableCfg
	localIds map[int32]struct{}
}

func NewIapTriggerPackTable() *IapTriggerPackTable {
	return &IapTriggerPackTable{
		records:  map[int32]*IapTriggerPackTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapTriggerPackTable) Get(key int32) *IapTriggerPackTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapTriggerPackTable) GetAll() map[int32]*IapTriggerPackTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapTriggerPackTable) put(key int32, value *IapTriggerPackTableCfg, local bool) *IapTriggerPackTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapTriggerPackTable) Range(f func(v *IapTriggerPackTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapTriggerPackTable) Filter(filterFuncs ...func(v *IapTriggerPackTableCfg) bool) map[int32]*IapTriggerPackTableCfg {
	filtered := map[int32]*IapTriggerPackTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapTriggerPackTable) FilterSlice(filterFuncs ...func(v *IapTriggerPackTableCfg) bool) []*IapTriggerPackTableCfg {
	filtered := []*IapTriggerPackTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapTriggerPackTable) FilterKeys(filterFuncs ...func(v *IapTriggerPackTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapTriggerPackTable) satisfied(v *IapTriggerPackTableCfg, filterFuncs ...func(v *IapTriggerPackTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapTriggerPackTable) setupIndexes() error {
	return nil
}

func (t *IapTriggerPackTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapTriggerPackTableCfg) bindRefs(c *Configs) {
	r.GroupRef = c.IapTriggerPackGroupTable.Get(r.Group)
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}
