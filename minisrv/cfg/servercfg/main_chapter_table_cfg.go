// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type MainChapterTableCfg struct {
	Id int32 `json:"Id"` // Id
}

func NewMainChapterTableCfg() *MainChapterTableCfg {
	return &MainChapterTableCfg{
		Id: 0,
	}
}

type MainChapterTable struct {
	records  map[int32]*MainChapterTableCfg
	localIds map[int32]struct{}
}

func NewMainChapterTable() *MainChapterTable {
	return &MainChapterTable{
		records:  map[int32]*MainChapterTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *MainChapterTable) Get(key int32) *MainChapterTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MainChapterTable) GetAll() map[int32]*MainChapterTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MainChapterTable) put(key int32, value *MainChapterTableCfg, local bool) *MainChapterTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MainChapterTable) Range(f func(v *MainChapterTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MainChapterTable) Filter(filterFuncs ...func(v *MainChapterTableCfg) bool) map[int32]*MainChapterTableCfg {
	filtered := map[int32]*MainChapterTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MainChapterTable) FilterSlice(filterFuncs ...func(v *MainChapterTableCfg) bool) []*MainChapterTableCfg {
	filtered := []*MainChapterTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MainChapterTable) FilterKeys(filterFuncs ...func(v *MainChapterTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MainChapterTable) satisfied(v *MainChapterTableCfg, filterFuncs ...func(v *MainChapterTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MainChapterTable) setupIndexes() error {
	return nil
}

func (t *MainChapterTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MainChapterTableCfg) bindRefs(c *Configs) {
}
