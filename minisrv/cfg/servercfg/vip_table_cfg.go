// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type VipTableCfg struct {
	Id              int32               `json:"Id"`            // Id
	StringId        string              `json:"StringId"`      // StringId
	Level           int32               `json:"Level"`         // 等级
	Max             bool                `json:"Max"`           // 最大
	Exp             int32               `json:"Exp"`           // 升级所需累计经验
	Benefits        []int32             `json:"Benefits"`      // 特权
	BenefitsRef     []*BenefitsTableCfg `json:"-"`             // 特权
	BenefitsValue   []float32           `json:"BenefitsValue"` // 特权值
	FreeGift        []*RewardKVS        `json:"FreeGift"`      // 每日免费礼包
	IapPackageId    int32               `json:"IapPackageId"`  // VIP专属礼包
	IapPackageIdRef *IapPackageTableCfg `json:"-"`             // VIP专属礼包
	Limit           PurchaseLimitType   `json:"Limit"`         // 限购类型
	Times           int32               `json:"Times"`         // 限购次数
}

func NewVipTableCfg() *VipTableCfg {
	return &VipTableCfg{
		Id:              0,
		StringId:        "",
		Level:           0,
		Max:             false,
		Exp:             0,
		Benefits:        []int32{},
		BenefitsRef:     []*BenefitsTableCfg{},
		BenefitsValue:   []float32{},
		FreeGift:        []*RewardKVS{},
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

type VipTable struct {
	records  map[int32]*VipTableCfg
	localIds map[int32]struct{}
}

func NewVipTable() *VipTable {
	return &VipTable{
		records:  map[int32]*VipTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *VipTable) Get(key int32) *VipTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *VipTable) GetAll() map[int32]*VipTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *VipTable) put(key int32, value *VipTableCfg, local bool) *VipTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *VipTable) Range(f func(v *VipTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *VipTable) Filter(filterFuncs ...func(v *VipTableCfg) bool) map[int32]*VipTableCfg {
	filtered := map[int32]*VipTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *VipTable) FilterSlice(filterFuncs ...func(v *VipTableCfg) bool) []*VipTableCfg {
	filtered := []*VipTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *VipTable) FilterKeys(filterFuncs ...func(v *VipTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *VipTable) satisfied(v *VipTableCfg, filterFuncs ...func(v *VipTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *VipTable) setupIndexes() error {
	return nil
}

func (t *VipTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *VipTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Benefits {
		cfgoRefRecord := c.BenefitsTable.Get(e)
		r.BenefitsRef = append(r.BenefitsRef, cfgoRefRecord)
	}
	for _, e := range r.FreeGift {
		e.bindRefs(c)
	}
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}
