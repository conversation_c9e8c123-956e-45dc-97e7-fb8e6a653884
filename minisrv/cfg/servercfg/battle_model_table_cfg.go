// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type BattleModelTableCfg struct {
	Id int32 `json:"Id"` // Id
}

func NewBattleModelTableCfg() *BattleModelTableCfg {
	return &BattleModelTableCfg{
		Id: 0,
	}
}

type BattleModelTable struct {
	records  map[int32]*BattleModelTableCfg
	localIds map[int32]struct{}
}

func NewBattleModelTable() *BattleModelTable {
	return &BattleModelTable{
		records:  map[int32]*BattleModelTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *BattleModelTable) Get(key int32) *BattleModelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *BattleModelTable) GetAll() map[int32]*BattleModelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *BattleModelTable) put(key int32, value *BattleModelTableCfg, local bool) *BattleModelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *BattleModelTable) Range(f func(v *BattleModelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *BattleModelTable) Filter(filterFuncs ...func(v *BattleModelTableCfg) bool) map[int32]*BattleModelTableCfg {
	filtered := map[int32]*BattleModelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *BattleModelTable) FilterSlice(filterFuncs ...func(v *BattleModelTableCfg) bool) []*BattleModelTableCfg {
	filtered := []*BattleModelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *BattleModelTable) FilterKeys(filterFuncs ...func(v *BattleModelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *BattleModelTable) satisfied(v *BattleModelTableCfg, filterFuncs ...func(v *BattleModelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *BattleModelTable) setupIndexes() error {
	return nil
}

func (t *BattleModelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *BattleModelTableCfg) bindRefs(c *Configs) {
}
