// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapPriceTableCfg struct {
	Id       int32  `json:"Id"`       // Id
	StringId string `json:"StringId"` // StringId
	Price    string `json:"Price"`    // 价格$
	Cny      int32  `json:"Cny"`      // 价格￥
	Krw      int32  `json:"Krw"`      // 价格₩
	Diamond  int32  `json:"Diamond"`  // 钻石
	LtcScore int32  `json:"LtcScore"` // 累充活动积分
}

func NewIapPriceTableCfg() *IapPriceTableCfg {
	return &IapPriceTableCfg{
		Id:       0,
		StringId: "",
		Price:    "",
		Cny:      0,
		Krw:      0,
		Diamond:  0,
		LtcScore: 0,
	}
}

type IapPriceTable struct {
	records  map[int32]*IapPriceTableCfg
	localIds map[int32]struct{}
}

func NewIapPriceTable() *IapPriceTable {
	return &IapPriceTable{
		records:  map[int32]*IapPriceTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapPriceTable) Get(key int32) *IapPriceTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapPriceTable) GetAll() map[int32]*IapPriceTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapPriceTable) put(key int32, value *IapPriceTableCfg, local bool) *IapPriceTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapPriceTable) Range(f func(v *IapPriceTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapPriceTable) Filter(filterFuncs ...func(v *IapPriceTableCfg) bool) map[int32]*IapPriceTableCfg {
	filtered := map[int32]*IapPriceTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapPriceTable) FilterSlice(filterFuncs ...func(v *IapPriceTableCfg) bool) []*IapPriceTableCfg {
	filtered := []*IapPriceTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapPriceTable) FilterKeys(filterFuncs ...func(v *IapPriceTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapPriceTable) satisfied(v *IapPriceTableCfg, filterFuncs ...func(v *IapPriceTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapPriceTable) setupIndexes() error {
	return nil
}

func (t *IapPriceTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapPriceTableCfg) bindRefs(c *Configs) {
}
