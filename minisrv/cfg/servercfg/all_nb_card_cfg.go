// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type AllNbCardCfg struct {
	Id       int32        `json:"Id"`       // Id
	StringId string       `json:"StringId"` // StringId
	TaskType TaskType     `json:"TaskType"` // 任务类型
	Formula  string       `json:"Formula"`  // 达成条件
	Value    int32        `json:"Value"`    // 值
	Reward   []*RewardKVS `json:"Reward"`   // 任务奖励
}

func NewAllNbCardCfg() *AllNbCardCfg {
	return &AllNbCardCfg{
		Id:       0,
		StringId: "",
		TaskType: TaskType(enumDefaultValue),
		Formula:  "",
		Value:    0,
		Reward:   []*RewardKVS{},
	}
}

type AllNbCard struct {
	records  map[int32]*AllNbCardCfg
	localIds map[int32]struct{}
}

func NewAllNbCard() *AllNbCard {
	return &AllNbCard{
		records:  map[int32]*AllNbCardCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *AllNbCard) Get(key int32) *AllNbCardCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *AllNbCard) GetAll() map[int32]*AllNbCardCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *AllNbCard) put(key int32, value *AllNbCardCfg, local bool) *AllNbCardCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *AllNbCard) Range(f func(v *AllNbCardCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *AllNbCard) Filter(filterFuncs ...func(v *AllNbCardCfg) bool) map[int32]*AllNbCardCfg {
	filtered := map[int32]*AllNbCardCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *AllNbCard) FilterSlice(filterFuncs ...func(v *AllNbCardCfg) bool) []*AllNbCardCfg {
	filtered := []*AllNbCardCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *AllNbCard) FilterKeys(filterFuncs ...func(v *AllNbCardCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *AllNbCard) satisfied(v *AllNbCardCfg, filterFuncs ...func(v *AllNbCardCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *AllNbCard) setupIndexes() error {
	return nil
}

func (t *AllNbCard) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *AllNbCardCfg) bindRefs(c *Configs) {
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}
