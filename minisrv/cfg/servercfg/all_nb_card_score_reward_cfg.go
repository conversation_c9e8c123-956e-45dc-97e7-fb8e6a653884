// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type AllNbCardScoreRewardCfg struct {
	Id       int32        `json:"Id"`       // Id
	StringId string       `json:"StringId"` // StringId
	Score    int32        `json:"Score"`    // 积分
	Reward   []*RewardKVS `json:"Reward"`   // 奖励
}

func NewAllNbCardScoreRewardCfg() *AllNbCardScoreRewardCfg {
	return &AllNbCardScoreRewardCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Reward:   []*RewardKVS{},
	}
}

type AllNbCardScoreReward struct {
	records  map[int32]*AllNbCardScoreRewardCfg
	localIds map[int32]struct{}
}

func NewAllNbCardScoreReward() *AllNbCardScoreReward {
	return &AllNbCardScoreReward{
		records:  map[int32]*AllNbCardScoreRewardCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *AllNbCardScoreReward) Get(key int32) *AllNbCardScoreRewardCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *AllNbCardScoreReward) GetAll() map[int32]*AllNbCardScoreRewardCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *AllNbCardScoreReward) put(key int32, value *AllNbCardScoreRewardCfg, local bool) *AllNbCardScoreRewardCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *AllNbCardScoreReward) Range(f func(v *AllNbCardScoreRewardCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *AllNbCardScoreReward) Filter(filterFuncs ...func(v *AllNbCardScoreRewardCfg) bool) map[int32]*AllNbCardScoreRewardCfg {
	filtered := map[int32]*AllNbCardScoreRewardCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *AllNbCardScoreReward) FilterSlice(filterFuncs ...func(v *AllNbCardScoreRewardCfg) bool) []*AllNbCardScoreRewardCfg {
	filtered := []*AllNbCardScoreRewardCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *AllNbCardScoreReward) FilterKeys(filterFuncs ...func(v *AllNbCardScoreRewardCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *AllNbCardScoreReward) satisfied(v *AllNbCardScoreRewardCfg, filterFuncs ...func(v *AllNbCardScoreRewardCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *AllNbCardScoreReward) setupIndexes() error {
	return nil
}

func (t *AllNbCardScoreReward) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *AllNbCardScoreRewardCfg) bindRefs(c *Configs) {
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}
