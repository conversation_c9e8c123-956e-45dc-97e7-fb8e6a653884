{"1": {"Id": 1, "StringId": "trigger_pack_level_pass_13_group", "CountDown": 24, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 13}, "2": {"Id": 2, "StringId": "trigger_pack_level_pass_23_group", "CountDown": 24, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 23}, "3": {"Id": 3, "StringId": "trigger_pack_level_pass_33_group", "CountDown": 48, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 33}, "4": {"Id": 4, "StringId": "trigger_pack_level_pass_43_group", "CountDown": 48, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 43}, "5": {"Id": 5, "StringId": "trigger_pack_level_pass_53_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 53}, "6": {"Id": 6, "StringId": "trigger_pack_level_pass_63_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 63}, "7": {"Id": 7, "StringId": "trigger_pack_level_pass_73_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 73}, "8": {"Id": 8, "StringId": "trigger_pack_level_pass_83_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 83}, "9": {"Id": 9, "StringId": "trigger_pack_level_pass_93_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 93}, "10": {"Id": 10, "StringId": "trigger_pack_level_pass_103_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 103}, "11": {"Id": 11, "StringId": "trigger_pack_level_pass_113_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 113}, "12": {"Id": 12, "StringId": "trigger_pack_level_pass_123_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 123}, "13": {"Id": 13, "StringId": "trigger_pack_level_pass_133_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 133}, "14": {"Id": 14, "StringId": "trigger_pack_level_pass_143_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 143}, "15": {"Id": 15, "StringId": "trigger_pack_level_pass_153_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 153}, "16": {"Id": 16, "StringId": "trigger_pack_level_pass_163_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 163}, "17": {"Id": 17, "StringId": "trigger_pack_level_pass_173_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 173}, "18": {"Id": 18, "StringId": "trigger_pack_level_pass_183_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 183}, "19": {"Id": 19, "StringId": "trigger_pack_level_pass_193_group", "CountDown": 72, "TriggerInterval": 9999, "TriggerPackType": 1, "Formula": "", "Value": 193}, "101": {"Id": 101, "StringId": "trigger_pack_hero_summon", "CountDown": 24, "TriggerInterval": 1, "TriggerPackType": 3, "Formula": "", "Value": 10}, "201": {"Id": 201, "StringId": "trigger_pack_gem_draw", "CountDown": 24, "TriggerInterval": 1, "TriggerPackType": 2, "Formula": "", "Value": 1}}