{"1": {"Id": 1, "OwnRank": {"Min": 1, "Max": 1}, "OppositeRank": [{"Min": 1, "Max": 3}, {"Min": 4, "Max": 6}, {"Min": 7, "Max": 9}, {"Min": 10, "Max": 12}, {"Min": 13, "Max": 15}], "OppositeWeight": [100, 100, 100, 100, 100]}, "2": {"Id": 2, "OwnRank": {"Min": 2, "Max": 2}, "OppositeRank": [{"Min": -1, "Max": -1}, {"Min": 1, "Max": 3}, {"Min": 4, "Max": 6}, {"Min": 7, "Max": 10}, {"Min": 11, "Max": 15}], "OppositeWeight": [100, 100, 100, 100, 100]}, "3": {"Id": 3, "OwnRank": {"Min": 3, "Max": 3}, "OppositeRank": [{"Min": -2, "Max": -2}, {"Min": -1, "Max": -1}, {"Min": 1, "Max": 4}, {"Min": 5, "Max": 8}, {"Min": 9, "Max": 15}], "OppositeWeight": [100, 100, 100, 100, 100]}, "4": {"Id": 4, "OwnRank": {"Min": 4, "Max": 4}, "OppositeRank": [{"Min": -3, "Max": -3}, {"Min": -2, "Max": -1}, {"Min": 1, "Max": 4}, {"Min": 5, "Max": 8}, {"Min": 9, "Max": 15}], "OppositeWeight": [100, 100, 100, 100, 100]}, "5": {"Id": 5, "OwnRank": {"Min": 5, "Max": 5}, "OppositeRank": [{"Min": -4, "Max": -4}, {"Min": -3, "Max": -1}, {"Min": 1, "Max": 4}, {"Min": 5, "Max": 8}, {"Min": 9, "Max": 15}], "OppositeWeight": [100, 100, 100, 100, 100]}, "6": {"Id": 6, "OwnRank": {"Min": 6, "Max": 6}, "OppositeRank": [{"Min": -5, "Max": -5}, {"Min": -4, "Max": -1}, {"Min": 1, "Max": 4}, {"Min": 5, "Max": 8}, {"Min": 9, "Max": 15}], "OppositeWeight": [100, 100, 100, 100, 100]}, "7": {"Id": 7, "OwnRank": {"Min": 7, "Max": 7}, "OppositeRank": [{"Min": -6, "Max": -6}, {"Min": -5, "Max": -1}, {"Min": 1, "Max": 5}, {"Min": 6, "Max": 10}, {"Min": 11, "Max": 20}], "OppositeWeight": [100, 100, 100, 100, 100]}, "8": {"Id": 8, "OwnRank": {"Min": 8, "Max": 8}, "OppositeRank": [{"Min": -7, "Max": -7}, {"Min": -6, "Max": -1}, {"Min": 1, "Max": 5}, {"Min": 6, "Max": 10}, {"Min": 11, "Max": 20}], "OppositeWeight": [100, 100, 100, 100, 100]}, "9": {"Id": 9, "OwnRank": {"Min": 9, "Max": 9}, "OppositeRank": [{"Min": -8, "Max": -8}, {"Min": -7, "Max": -1}, {"Min": 1, "Max": 5}, {"Min": 6, "Max": 10}, {"Min": 11, "Max": 20}], "OppositeWeight": [100, 100, 100, 100, 100]}, "10": {"Id": 10, "OwnRank": {"Min": 10, "Max": 10}, "OppositeRank": [{"Min": -9, "Max": -9}, {"Min": -8, "Max": -1}, {"Min": 1, "Max": 5}, {"Min": 6, "Max": 10}, {"Min": 11, "Max": 20}], "OppositeWeight": [100, 100, 100, 100, 100]}, "11": {"Id": 11, "OwnRank": {"Min": 11, "Max": 20}, "OppositeRank": [{"Min": -10, "Max": -6}, {"Min": -5, "Max": -1}, {"Min": 1, "Max": 5}, {"Min": 6, "Max": 10}, {"Min": 11, "Max": 20}], "OppositeWeight": [100, 100, 100, 100, 100]}, "12": {"Id": 12, "OwnRank": {"Min": 21, "Max": 30}, "OppositeRank": [{"Min": -20, "Max": -11}, {"Min": -10, "Max": -1}, {"Min": 1, "Max": 10}, {"Min": 11, "Max": 20}, {"Min": 21, "Max": 40}], "OppositeWeight": [100, 100, 100, 100, 100]}, "13": {"Id": 13, "OwnRank": {"Min": 31, "Max": 40}, "OppositeRank": [{"Min": -30, "Max": -21}, {"Min": -20, "Max": -11}, {"Min": -10, "Max": -1}, {"Min": 1, "Max": 20}, {"Min": 21, "Max": 40}], "OppositeWeight": [100, 100, 100, 100, 100]}, "14": {"Id": 14, "OwnRank": {"Min": 41, "Max": 50}, "OppositeRank": [{"Min": -40, "Max": -31}, {"Min": -30, "Max": -16}, {"Min": -15, "Max": -1}, {"Min": 1, "Max": 20}, {"Min": 21, "Max": 40}], "OppositeWeight": [100, 100, 100, 100, 100]}, "15": {"Id": 15, "OwnRank": {"Min": 51, "Max": 100}, "OppositeRank": [{"Min": -40, "Max": -31}, {"Min": -30, "Max": -16}, {"Min": -15, "Max": -1}, {"Min": 1, "Max": 20}, {"Min": 21, "Max": 40}], "OppositeWeight": [100, 100, 100, 100, 100]}, "16": {"Id": 16, "OwnRank": {"Min": 101, "Max": 200}, "OppositeRank": [{"Min": -50, "Max": -41}, {"Min": -40, "Max": -21}, {"Min": -20, "Max": -1}, {"Min": 1, "Max": 20}, {"Min": 21, "Max": 50}], "OppositeWeight": [100, 100, 100, 100, 100]}, "17": {"Id": 17, "OwnRank": {"Min": 201, "Max": 500}, "OppositeRank": [{"Min": -100, "Max": -51}, {"Min": -50, "Max": -26}, {"Min": -25, "Max": -1}, {"Min": 1, "Max": 30}, {"Min": 31, "Max": 60}], "OppositeWeight": [100, 100, 100, 100, 100]}, "18": {"Id": 18, "OwnRank": {"Min": 501, "Max": 1000}, "OppositeRank": [{"Min": -200, "Max": -101}, {"Min": -100, "Max": -51}, {"Min": -50, "Max": -1}, {"Min": 1, "Max": 50}, {"Min": 51, "Max": 100}], "OppositeWeight": [100, 100, 100, 100, 100]}, "19": {"Id": 19, "OwnRank": {"Min": 1001, "Max": 2000}, "OppositeRank": [{"Min": -500, "Max": -251}, {"Min": -250, "Max": -126}, {"Min": -125, "Max": -1}, {"Min": 1, "Max": 125}, {"Min": 126, "Max": 250}], "OppositeWeight": [100, 100, 100, 100, 100]}, "20": {"Id": 20, "OwnRank": {"Min": 2001, "Max": 5000}, "OppositeRank": [{"Min": -1000, "Max": -501}, {"Min": -500, "Max": -251}, {"Min": -250, "Max": -1}, {"Min": 1, "Max": 250}, {"Min": 251, "Max": 500}], "OppositeWeight": [100, 100, 100, 100, 100]}, "21": {"Id": 21, "OwnRank": {"Min": 5001, "Max": 10000}, "OppositeRank": [{"Min": -1000, "Max": -501}, {"Min": -500, "Max": -251}, {"Min": -250, "Max": -251}, {"Min": -250, "Max": -125}, {"Min": -124, "Max": -1}], "OppositeWeight": [100, 100, 100, 100, 100]}}