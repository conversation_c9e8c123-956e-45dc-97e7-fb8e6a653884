{"1": {"Id": 1, "StringId": "trigger_pack_level_pass_13", "Group": 1, "IapPackageId": 801, "Limit": 4, "Times": 1}, "2": {"Id": 2, "StringId": "trigger_pack_level_pass_23", "Group": 2, "IapPackageId": 802, "Limit": 4, "Times": 1}, "3": {"Id": 3, "StringId": "trigger_pack_level_pass_33", "Group": 3, "IapPackageId": 803, "Limit": 4, "Times": 1}, "4": {"Id": 4, "StringId": "trigger_pack_level_pass_43", "Group": 4, "IapPackageId": 804, "Limit": 4, "Times": 1}, "5": {"Id": 5, "StringId": "trigger_pack_level_pass_53", "Group": 5, "IapPackageId": 805, "Limit": 4, "Times": 1}, "6": {"Id": 6, "StringId": "trigger_pack_level_pass_63", "Group": 6, "IapPackageId": 806, "Limit": 4, "Times": 1}, "7": {"Id": 7, "StringId": "trigger_pack_level_pass_73", "Group": 7, "IapPackageId": 807, "Limit": 4, "Times": 1}, "8": {"Id": 8, "StringId": "trigger_pack_level_pass_83", "Group": 8, "IapPackageId": 808, "Limit": 4, "Times": 1}, "9": {"Id": 9, "StringId": "trigger_pack_level_pass_93", "Group": 9, "IapPackageId": 809, "Limit": 4, "Times": 1}, "10": {"Id": 10, "StringId": "trigger_pack_level_pass_103", "Group": 10, "IapPackageId": 810, "Limit": 4, "Times": 1}, "11": {"Id": 11, "StringId": "trigger_pack_level_pass_113", "Group": 11, "IapPackageId": 811, "Limit": 4, "Times": 1}, "12": {"Id": 12, "StringId": "trigger_pack_level_pass_123", "Group": 12, "IapPackageId": 812, "Limit": 4, "Times": 1}, "13": {"Id": 13, "StringId": "trigger_pack_level_pass_133", "Group": 13, "IapPackageId": 813, "Limit": 4, "Times": 1}, "14": {"Id": 14, "StringId": "trigger_pack_level_pass_143", "Group": 14, "IapPackageId": 814, "Limit": 4, "Times": 1}, "15": {"Id": 15, "StringId": "trigger_pack_level_pass_153", "Group": 15, "IapPackageId": 815, "Limit": 4, "Times": 1}, "16": {"Id": 16, "StringId": "trigger_pack_level_pass_163", "Group": 16, "IapPackageId": 816, "Limit": 4, "Times": 1}, "17": {"Id": 17, "StringId": "trigger_pack_level_pass_173", "Group": 17, "IapPackageId": 817, "Limit": 4, "Times": 1}, "18": {"Id": 18, "StringId": "trigger_pack_level_pass_183", "Group": 18, "IapPackageId": 818, "Limit": 4, "Times": 1}, "19": {"Id": 19, "StringId": "trigger_pack_level_pass_193", "Group": 19, "IapPackageId": 819, "Limit": 4, "Times": 1}, "101": {"Id": 101, "StringId": "trigger_pack_hero_summon_1", "Group": 101, "IapPackageId": 820, "Limit": 1, "Times": 1}, "102": {"Id": 102, "StringId": "trigger_pack_hero_summon_2", "Group": 101, "IapPackageId": 821, "Limit": 1, "Times": 1}, "103": {"Id": 103, "StringId": "trigger_pack_hero_summon_3", "Group": 101, "IapPackageId": 822, "Limit": 1, "Times": 1}, "201": {"Id": 201, "StringId": "trigger_pack_gem_draw_1", "Group": 201, "IapPackageId": 823, "Limit": 1, "Times": 1}, "202": {"Id": 202, "StringId": "trigger_pack_gem_draw_2", "Group": 201, "IapPackageId": 824, "Limit": 1, "Times": 1}, "203": {"Id": 203, "StringId": "trigger_pack_gem_draw_3", "Group": 201, "IapPackageId": 825, "Limit": 1, "Times": 1}}