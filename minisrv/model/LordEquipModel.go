package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sync"
)

func init() {
	orm.RegisterModel[*LordEquip](nil)
}

type LordEquip struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.LordEquipRecord
	uid int64
}

func (u *LordEquip) Locker() locker.RWLocker {
	return nil
}

func (u *LordEquip) LockPriority() int {
	return int(u.Uid())
}

func (u *LordEquip) IsGroup() bool {
	return false
}

func GetAllLordEquipModels(ctx context.Context, uid int64) []*LordEquip {
	lordEquip, _ := orm.GetAll[*LordEquip](ctx, uid)
	return lordEquip
}

func GetLordEquipModel(ctx context.Context, uid int64, typeId int32) *LordEquip {
	lordEquip, err := orm.Get[*LordEquip](ctx, uid, typeId)
	if err != nil {
		panic(err)
	}
	if lordEquip == nil {
		equipGradeLines := cfg_mgr.Cfg.LordEquipGradeTable.FilterSlice(func(v *servercfg.LordEquipGradeTableCfg) bool {
			return v.Grade == 1 && v.LordEquipType == typeId
		})
		equipLevelLines := cfg_mgr.Cfg.LordEquipTable.FilterSlice(func(v *servercfg.LordEquipTableCfg) bool {
			return v.Level == 0 && v.LordEquipType == typeId
		})
		lordEquip, err = orm.Create[*LordEquip](ctx, &minirpc.LordEquip{
			Uid:      uid,
			TypeId:   typeId,
			ConfigId: equipLevelLines[0].Id,
			GeneInfo: map[int32]int32{},
			GradeId:  equipGradeLines[0].Id,
		})
	}
	return lordEquip
}
func ExportLordEquips(ctx context.Context, uid int64) ([]*minirpc.LordEquip, error) {
	lordEquips, err := orm.GetAll[*LordEquip](ctx, uid)
	var lordEquipInfos []*minirpc.LordEquip
	if lordEquips != nil {
		for _, v := range lordEquips {
			lordEquipInfos = append(lordEquipInfos, v.Snapshoot().(*minirpc.LordEquip))

		}
	}
	return lordEquipInfos, err
}
