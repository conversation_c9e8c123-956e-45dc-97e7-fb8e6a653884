#!/usr/bin/env bash

:<<!
  minisrv 服务运行脚本使用说明
  * 在supervisor配置 command=pathTo/gog-knights/minisrv/run.sh
  * 更新fix版本的程序，上传到 gog-knights/minisrv/minisrv.update

    1. supervisor自动拉起时，脚本自动检查是否带.update后缀的程序，如果有将当前minisrv拷贝到minisrv.backup
    2. 将minisrv.update 拷贝覆盖到 minisrv
    3. 运行minisrv服务
!

PROGRAM="minisrv"
PROGRAM_UPDATE="minisrv.update"
PROGRAM_BACKUP="minisrv.backup"
WORK_PATH=$(dirname ${0})
if [ -f "$WORK_PATH/$PROGRAM_UPDATE" ]; then
    mv -f "$WORK_PATH/$PROGRAM" "$WORK_PATH/$PROGRAM_BACKUP"
    mv -f "$WORK_PATH/$PROGRAM_UPDATE" "$WORK_PATH/$PROGRAM"
    sleep 5
fi
export WATCHER_CONF_GROUP=develop
cd $WORK_PATH
"$WORK_PATH/$PROGRAM" run --kingdom=1 --addrGrpc=:10011 --addrPProf=:7081 --debugMsg=true --test=true
