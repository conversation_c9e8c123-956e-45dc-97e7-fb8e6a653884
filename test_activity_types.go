package main

import (
	"fmt"
)

// 模拟 ActivityTypeInfo 结构
type ActivityTypeInfo struct {
	ID   int32  `json:"id"`
	Name string `json:"name"`
	Type int32  `json:"type"`
}

// 模拟响应结构
type Response struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// 模拟 IapBoothType 枚举
type IapBoothType int32

const (
	IapBoothType_DiamondShop IapBoothType = 1  // 钻石商店
	IapBoothType_DailySale   IapBoothType = 2  // 每日特惠
	IapBoothType_RegularPack IapBoothType = 3  // 常规礼包
	IapBoothType_RegularBp   IapBoothType = 4  // 常规BP
	IapBoothType_NoAds       IapBoothType = 5  // 免广告特权
	IapBoothType_2X          IapBoothType = 6  // 加速特权
	IapBoothType_MonthCard   IapBoothType = 7  // 月卡
	IapBoothType_Fund        IapBoothType = 8  // 成长基金
	IapBoothType_Sign        IapBoothType = 9  // 登录好礼
	IapBoothType_TurnTable   IapBoothType = 10 // 英雄转盘
	IapBoothType_Sign7       IapBoothType = 11 // 7日签到
)

// 测试 getIapBoothTypeName 函数
func testGetIapBoothTypeName() {
	fmt.Println("=== 测试 IapBoothType 中文名称映射 ===")

	testCases := []struct {
		boothType IapBoothType
		expected  string
	}{
		{IapBoothType_DiamondShop, "钻石商店"},
		{IapBoothType_DailySale, "每日特惠"},
		{IapBoothType_RegularPack, "常规礼包"},
		{IapBoothType_TurnTable, "英雄转盘"},
		{IapBoothType_Sign7, "7日签到"},
	}

	for _, tc := range testCases {
		result := getIapBoothTypeName(tc.boothType)
		status := "✓"
		if result != tc.expected {
			status = "✗"
		}
		fmt.Printf("%s IapBoothType(%d) -> %s (期望: %s)\n", status, tc.boothType, result, tc.expected)
	}
}

// 从 gmApi 包复制的函数
func getIapBoothTypeName(boothType servercfg.IapBoothType) string {
	switch boothType {
	case servercfg.IapBoothType_DiamondShop:
		return "钻石商店"
	case servercfg.IapBoothType_DailySale:
		return "每日特惠"
	case servercfg.IapBoothType_RegularPack:
		return "常规礼包"
	case servercfg.IapBoothType_RegularBp:
		return "常规BP"
	case servercfg.IapBoothType_NoAds:
		return "免广告特权"
	case servercfg.IapBoothType_2X:
		return "加速特权"
	case servercfg.IapBoothType_MonthCard:
		return "月卡"
	case servercfg.IapBoothType_Fund:
		return "成长基金"
	case servercfg.IapBoothType_Sign:
		return "登录好礼"
	case servercfg.IapBoothType_TurnTable:
		return "英雄转盘"
	case servercfg.IapBoothType_Sign7:
		return "7日签到"
	default:
		return "未知类型"
	}
}

// 测试 GetActivityTypes API
func testGetActivityTypesAPI() {
	fmt.Println("\n=== 测试 GetActivityTypes API ===")

	// 创建一个模拟的 HTTP 请求
	req := httptest.NewRequest("GET", "/gm/api/activity-types", nil)
	req.Header.Set("X-GM-Token", "test-token")

	// 创建一个响应记录器
	w := httptest.NewRecorder()

	// 调用 API 函数
	gmApi.GetActivityTypes(w, req, httprouter.Params{})

	// 检查响应
	resp := w.Result()
	defer resp.Body.Close()

	fmt.Printf("状态码: %d\n", resp.StatusCode)

	// 解析响应 JSON
	var response Response
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		return
	}

	fmt.Printf("响应码: %d\n", response.Code)
	fmt.Printf("消息: %s\n", response.Message)

	if types, ok := response.Data["types"].([]interface{}); ok {
		fmt.Printf("活动类型数量: %d\n", len(types))
		for i, typeData := range types {
			if typeMap, ok := typeData.(map[string]interface{}); ok {
				id := typeMap["id"]
				name := typeMap["name"]
				typeValue := typeMap["type"]
				fmt.Printf("  %d. ID: %v, Name: %v, Type: %v\n", i+1, id, name, typeValue)
			}
		}
	}
}

func main() {
	testGetIapBoothTypeName()
	testGetActivityTypesAPI()

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("如果所有测试都通过，说明活动类型映射修复成功！")
}
